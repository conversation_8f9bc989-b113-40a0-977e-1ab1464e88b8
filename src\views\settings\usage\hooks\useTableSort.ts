import { Sort, TableInstance } from "element-plus"

export function useTableSort(callback: () => void, tableRef?: Ref<TableInstance | undefined>) {
  // 排序相关
  const sortField = ref('')
  const sortOrder = ref<'ASC' | 'DESC' | ''>('')

  function sortChange({ prop, order }: Sort) {
    let isAsc: 'ASC' | 'DESC' | '' = ''
    if (order === 'ascending') {
      isAsc = 'ASC'
    } else if (order === 'descending') {
      isAsc = 'DESC'
    }
    sortField.value = prop
    sortOrder.value = isAsc
    if (callback) {
      callback()
    }
  }

  function resetSort() {
    sortField.value = ''
    sortOrder.value = ''

    // 清除表格 UI 上的排序状态
    tableRef?.value?.clearSort()
  }

  return {
    sortField,
    sortOrder,
    resetSort,
    sortChange,
  }
}