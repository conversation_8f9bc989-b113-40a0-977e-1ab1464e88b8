<script setup lang="ts">
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart } from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  GraphicComponent,
  DataZoomComponent,
} from 'echarts/components'

// 注册 ECharts 必要的组件
use([
  CanvasRenderer,
  BarChart,
  GridComponent,
  TooltipComponent,
  GraphicComponent,
  DataZoomComponent,
])

// 定义组件的 props
interface Props {
  xAxisData: string[]
  seriesData: number[]
}

const props = defineProps<Props>()

// 定义图表配置
const chartOption = computed(() => ({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  grid: {
    top: 10,
    right: 0,
    bottom: 55,
    left: '5%',
  },
  dataZoom: [
    {
      type: 'inside', // 内置型数据区域缩放组件
      start: 0,
      end: 100,
    },
    {
      type: 'slider', // 滑动条型数据区域缩放组件
      start: 0,
      end: 100,
      height: 12, // 滑动条高度
      bottom: 5, // 距离底部距离
    },
  ],
  xAxis: {
    type: 'category',
    data: props.xAxisData,
    axisLabel: {
      lineHeight: 16, // 设置行高
      formatter: function (value) {
        // 根据实际数据格式调整解析逻辑
        const parts = value.split(' ')
        if (parts.length >= 2) {
          return `${parts[0]}\n${parts.slice(1)}`
        }
        return value
      },
    },
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false,
    },
  },
  series: [
    {
      type: 'bar',
      data: props.seriesData,
      itemStyle: {
        color: '#4865E8', // 设置柱状图颜色
        borderRadius: [4, 4, 4, 4], // 设置柱状图顶部圆角，[左上，右上，右下，左下]
      },
      barMaxWidth: 10, // 设置柱状图的最大宽度为 25px
    },
  ],
}))
</script>

<template>
  <div class="chart">
    <v-chart :option="chartOption" autoresize />
  </div>
</template>

<style lang="less" scoped>
.chart {
  height: 100%;
  margin-bottom: 24px;
}
</style>
