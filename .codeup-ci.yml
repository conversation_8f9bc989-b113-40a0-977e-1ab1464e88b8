image: node:18-alpine

stages:
  - build
  - deploy

variables:
  # 跳过二进制下载
  ELECTRON_SKIP_BINARY_DOWNLOAD: "1"
  PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: "1"
  # 使用国内镜像
  NPM_CONFIG_REGISTRY: "https://registry.npmmirror.com/"
  NPM_CONFIG_ELECTRON_MIRROR: "https://npmmirror.com/mirrors/electron/"

cache:
  paths:
    - node_modules/
    - .npm/

before_script:
  - echo "设置 npm 配置..."
  - npm config set registry https://registry.npmmirror.com/
  - npm config set electron_mirror https://npmmirror.com/mirrors/electron/
  - npm config set electron_builder_binaries_mirror https://npmmirror.com/mirrors/electron-builder-binaries/
  - npm config set cache .npm
  - npm config set prefer-offline true

build_job:
  stage: build
  script:
    - echo "开始构建..."
    - echo "跳过 electron 相关依赖安装"
    - export ELECTRON_SKIP_BINARY_DOWNLOAD=1
    - export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1
    
    # 使用云效专用的 package.json
    - cp package.ci.json package.json
    
    # 安装依赖
    - npm ci --production=false --ignore-scripts
    
    # 构建项目
    - npm run build:stage
    
    - echo "构建完成"
  artifacts:
    paths:
      - dist/
    expire_in: 1 hour
  only:
    - master
    - main
    - develop

deploy_job:
  stage: deploy
  script:
    - echo "部署到服务器..."
    # 这里添加你的部署脚本
  dependencies:
    - build_job
  only:
    - master
    - main
