/** 分页结果 */
export interface PageResult<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
}

/** 外部接口项 */
export interface OutwardItem {
  id: string;
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  status: 'ENABLED' | 'DISABLED';
  description?: string;
  createdAt: string;
  updatedAt: string;
}
/**
 * 发布类型
 */
export enum OutwardType {
  API = "API",
  Dingtalk = "DINGTALK",
  EaseIdea = "EASE_IDEA",
  Feishu = "FEISHU",
  Floating = "FLOATING",
  Web = "WEB",
  Wechat = "WECHAT",
}
/** 分页查询参数 */
export interface OutwardQueryParam {
  firstNum?: number;
  /**
   * 排序的方向desc或者asc
   */
  isAsc?: string;
  /**
   * 名称
   */
  name?: string;
  /**
   * 排序列
   */
  orderByColumn?: string;
  /**
   * 发布类型
   */
  outwardType?: OutwardType;
  /**
   * 当前页数
   */
  pageNum: number;
  /**
   * 分页大小
   */
  pageSize?: number;
}

/**
 * OutwardVo，应用发布
 * <p>
 * Author: miracle
 * Date: 2025/7/15 下午1:55
 */
export interface OutwardVo {
  /**
     * 关联助手
     */
  agentId?: number;
  /**
   * 关联助手是否最新
   */
  agentIsNew?: boolean;
  /**
   * 关联助手名称
   */
  agentName?: string;
  /**
   * 创建人名称
   */
  createByName?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 截止有效期
   */
  deadline?: string;
  /**
   * 表情符号
   */
  agentEmoji?: string;
  /**
   * 背景颜色
   */
  agentBackgroundColor?: string;
  /**
   * 是否启用
   */
  enabled?: boolean;
  /**
   * 主键
   */
  id?: number;
  /**
   * 限制模型消耗
   */
  maxTokens?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 发布类型
   */
  outwardType: { code: OutwardType };
  /**
   * 载荷
   */
  payload?: { [key: string]: string };
  /**
   * 备注
   */
  remark?: string;
  /**
   * 租户编号
   */
  tenantId?: string;
  /**
   * 累计模型消耗
   */
  tokens?: number;
  /**
   * 是否需要授权
   */
  needAuth?: boolean;
}

/** 分页查询结果 */
export type OutwardQueryResult = PageResult<OutwardItem>;

/** 新增/编辑外部接口参数 */
export interface OutwardSaveParam {
  /**
     * 关联助手
     */
  agentId: number;
  /**
   * 截止有效期
   */
  deadline: string;
  /**
   * 是否启用
   */
  enabled?: boolean;
  /**
   * 主键
   */
  id?: number;
  /**
   * 限制模型消耗
   */
  maxTokens: number;
  /**
   * 名称
   */
  name: string;
  /**
   * 发布类型
   */
  outwardType: OutwardType;
  /**
   * 载荷
   */
  payload: { [key: string]: string };
  /**
   * 备注
   */
  remark?: string;
}

/** 接口使用统计 */
export interface OutwardUseStat {
  /**
    * 日期
    */
  day?: Date;
  /**
   * 用量
   */
  useTokens?: number;
}

/**
 * ConversationVo
 */
export interface ConversationVo {
  /**
   * 所属助手
   */
  agentId?: number;
  id?: string;
  /**
   * 是否开启知识库
   */
  kbEnableFlag?: boolean;
  /**
   * 关联知识库列表
   */
  kbList?: KnowledgeBaseVo[];
  /**
   * 上次对话id
   */
  lastChatId?: number;
  /**
   * 上次对话时间
   */
  lastChatTime?: Date;
  /**
   * 上次对话问题
   */
  lastQuestion?: string;
  /**
   * 最大上下文数量
   */
  maxContextCount?: number;
  /**
   * 最大召回数
   */
  maxResults?: number;
  /**
   * 最大回复长度
   */
  maxTokens?: number;
  /**
   * 最低匹配度
   */
  minScore?: number;
  /**
   * 模型id
   */
  modelId?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * rerank模型id
   */
  rerankModelId?: number;
  /**
   * rerank模型id
   */
  rerankModelName?: string;
  /**
   * 开启单次回复限制
   */
  singleReplyLimitFlag?: boolean;
  /**
   * 副标题
   */
  subtitle?: string;
  /**
   * 系统提示词
   */
  systemMessage?: string;
  /**
   * 随机性
   */
  temperature?: number;
  /**
   * 消耗的token数量
   */
  tokens?: number;
  [property: string]: any;
}

/**
 * KnowledgeBaseVo
 */
export interface KnowledgeBaseVo {
  /**
   * 描述
   */
  description?: string;
  /**
   * 编辑时间
   */
  editTime?: Date;
  /**
   * 是否启用
   */
  enabled?: boolean;
  /**
   * 主键
   */
  id?: number;
  /**
   * 模型是否还在
   * true 在
   * false 不在
   */
  modelExist?: boolean;
  /**
   * 模型id
   */
  modelId?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 权限列表
   */
  permissionList?: KnowledgeBasePermission[];
  /**
   * 分享权限等级 1-仅查看 2-可编辑
   */
  sharePermissionLevel?: string;
  /**
   * 分享状态 0-私人 1-全体共享 2-指定部门共享 3-指定成员共享
   */
  shareStatus?: number;
  /**
   * 大小
   */
  size?: number;
  /**
   * 类型 1-文本 2-图像
   */
  type?: number;
  /**
   * 所有者
   */
  userId?: number;
  /**
   * 所有者名称
   */
  userName?: string;
  [property: string]: any;
}

/**
 * KnowledgeBasePermission，知识库部门共享权限关联
 */
export interface KnowledgeBasePermission {
  /**
   * 知识库id
   */
  kbId?: number;
  /**
   * 0-不可见 1-仅查看 2-可编辑
   */
  permissionLevel?: number;
  /**
   * 权限目标 ID
   */
  targetId?: number;
  /**
   * 权限目标类型 1-部门 2-用户
   */
  targetType?: string;
  [property: string]: any;
}
