<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import type { DrawerProps } from 'element-plus'
import { Icon } from '@iconify/vue'
import { getBizKbItemSplitList, postBizKbItemEnableSlice } from '@/api/knowledge-base'
import '@/typings/knowledge-base.d.ts'
import { useKnowledgeBaseEditPermission } from '@/hooks/useKnowledgeBaseEditPermission'
import EnableSwitch from '@/components/common/EnableSwitch.vue'
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
const props = withDefaults(
  defineProps<{
    docInfo?: KnowledgeBase.KnowledgeBaseItemVo
    kbId: string
  }>(),
  {
    docInfo: () => ({}) as KnowledgeBase.KnowledgeBaseItemVo,
  },
)
const emits = defineEmits(['changeMode'])
defineOptions({
  name: '',
})
function backToTable() {
  emits('changeMode', 'table')
}

const isExpanded = ref(false)
const searchValue = ref('')
const inputRef = ref()

const expandSearch = () => {
  isExpanded.value = true
  // 等待 DOM 更新后自动聚焦
  setTimeout(() => {
    inputRef.value?.focus()
  })
}

const handleBlur = () => {
  if (!searchValue.value) {
    isExpanded.value = false
  }
}

const total = ref(0)
// 查询参数
// const queryParams = ref({
//   pageNum: 1,
//   pageSize: 10,
//   name: '',
// })

const segmentsList = ref<(KnowledgeBase.EmbeddingStoreVo & { enabled: boolean })[]>([])
const filteredSegments = computed(() => {
  if (!searchValue.value) return segmentsList.value
  return segmentsList.value.filter(
    item => item.text && item.text.toLowerCase().includes(searchValue.value.toLowerCase()),
  )
})

function highlightText(text: string) {
  if (!searchValue.value) return text
  const regex = new RegExp(searchValue.value, 'gi')
  return text.replace(regex, match => `<span class="highlight">${match}</span>`)
}
const loading = ref(false)
/**
 * 获取文件分段列表
 */
function getSegmentsList(enabled?: boolean) {
  loading.value = true
  getBizKbItemSplitList<KnowledgeBase.EmbeddingStoreVo[]>({
    kbItemId: props.docInfo?.id,
    enabled,
    // enabled: props.docInfo?.enabled,
  })
    .then(res => {
      if (res.code === 200) {
        const tempList = res.data || {}

        segmentsList.value = tempList.map(t => ({
          ...t,
          enabled: t.metadata?.enabled === '1',
        }))
      }
    })
    .finally(() => {
      loading.value = false
    })
}
const displayType = ref('all')

const picked = ref('all')
watch(
  picked,
  () => {
    switch (picked.value) {
      case 'all':
        getSegmentsList()
        break
      case 'enabled':
        getSegmentsList(true)
        break
      case 'forbidden':
        getSegmentsList(false)
        break
    }
    getSegmentsList
  },
  { immediate: true },
)
const drawer = ref(false)
const direction = ref<DrawerProps['direction']>('rtl')
const handleClose = () => {
  ElMessageBox.confirm('确认关闭吗', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      // done()
      drawer.value = false
    })
    .catch(() => {
      // catch error
    })
}

const handleEnable = (id: string, enabled: boolean) => {
  return postBizKbItemEnableSlice({
    kbItemIdList: [props.docInfo.id],
    embeddingIdList: [id],
    enabled,
  })
}

const canEditHere = useKnowledgeBaseEditPermission(props.kbId)
</script>

<template>
  <div class="flex justify-between items-center mb-[24px]">
    <div>
      <Icon
        class="size-[28px] text-[#4865E8] inline-block mr-[14px]"
        icon="famicons:arrow-back-circle-outline"
        @click="backToTable"
      />
      <img class="size-[24px] inline-block mr-[8px]" src="@/assets/word.png" alt="" />
      {{ docInfo.name }}
    </div>

    <div class="flex items-center gap-4">
      <el-radio-group v-model="displayType">
        <el-radio-button label="全文" value="all" />
        <el-radio-button label="省略" value="ell" />
      </el-radio-group>
      <!-- 产品说，批量操作先隐藏 -->
      <!-- <el-dropdown trigger="click">
        <button class="op-btn" size="small" :disabled="!checkList.length">
         <i class="iconfont iconfont-c icon-piliang mr-2"></i>
          批量操作
        </button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item> 启用 </el-dropdown-item>
            <el-dropdown-item> 禁用 </el-dropdown-item>
            <el-dropdown-item> 删除 </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown> -->
      <div v-if="canEditHere" class="search-container">
        <div v-if="!isExpanded" class="search-icon" @click="expandSearch">
          <Icon class="size-4 inline-block text-[#4865E8]" icon="iconamoon:search-bold" />
        </div>

        <el-input
          v-else
          ref="inputRef"
          v-model="searchValue"
          class="search-input"
          :class="{ expanded: isExpanded }"
          placeholder="请输入搜索内容"
          clearable
          @blur="handleBlur"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
      <el-dropdown v-if="canEditHere">
        <button class="op-btn secondary" size="small">
          <img class="size-5 inline-block" src="@/assets/knowledge/filter-icon.png" alt="" />
        </button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item>
              <input id="all" v-model="picked" class="mr-[4px]" type="radio" value="all" />
              <label for="all" class="text-[#30343a] text-[14px]">所有</label>
            </el-dropdown-item>
            <el-dropdown-item>
              <input id="enabled" v-model="picked" class="mr-[4px]" type="radio" value="enabled" />
              <label for="enabled" class="text-[#30343a] text-[14px]">启用</label>
            </el-dropdown-item>
            <el-dropdown-item>
              <input
                id="forbidden"
                v-model="picked"
                class="mr-[4px]"
                type="radio"
                value="forbidden"
              />
              <label for="forbidden" class="text-[#30343a] text-[14px]">禁用</label>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <button v-if="canEditHere" class="op-btn primary" @click="drawer = true">
        <img class="size-5 inline-block" src="@/assets/knowledge/add-icon-white.png" alt="" />
      </button>
    </div>
  </div>
  <div class="segmentation-list" v-loading="loading">
    <!-- <el-checkbox-group class="segmentation-checkbox-group" v-model="checkList"> -->
    <DynamicScroller
      v-if="filteredSegments.length"
      :items="filteredSegments"
      :min-item-size="displayType === 'ell' ? 65.33 : 150"
      :emit-update="true"
      key-field="embeddingId"
      class="scroller"
    >
      <template v-slot="{ item: segments, index, active }">
        <DynamicScrollerItem
          :item="segments"
          :active="active"
          :data-index="index"
          :size-dependencies="[segments.text]"
        >
          <div class="pb-4">
            <div class="segmentation-card">
              <div class="flex-1 pl-4 pr-6" :class="{ ell: displayType === 'ell' }">
                <span v-html="highlightText(segments.text)" />
              </div>
              <EnableSwitch
                v-if="canEditHere"
                v-model="segments.enabled"
                :handle-func-promise="() => handleEnable(segments.embeddingId, segments.enabled)"
              />
            </div>
          </div>
        </DynamicScrollerItem>
      </template>
    </DynamicScroller>
    <el-empty
      v-if="filteredSegments.length === 0"
      :description="searchValue ? '未查询到相关数据' : '暂无数据'"
    />
    <!-- </el-checkbox-group> -->
  </div>
  <!-- <Pagination
    v-model:page="queryParams.pageNum"
    v-model:limit="queryParams.pageSize"
    :total="total"
    @pagination="getSegmentsList"
  /> -->
  <el-drawer
    v-model="drawer"
    header-class="dataset-drawer-header"
    title="创建解析快"
    :direction="direction"
    :before-close="handleClose"
  >
    <el-input
      class="no-border"
      maxlength="1000"
      type="textarea"
      resize="none"
      :autosize="{ minRows: 20 }"
    />
    <template #footer>
      <div style="flex: auto">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleClose">保存</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<style lang="less" scoped>
.op-btn {
  padding: 0 10px;
  height: 40px;
  line-height: 38px;
  border-radius: 8px;
  border: 1px solid #dadde8;

  &.secondary {
    background-color: #dadde8;
  }
  &.primary {
    background-color: #4c5cec;
  }

  &[disabled] {
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.search-container {
  display: flex;
  align-items: center;
  border: 1px solid #dadde8;
  border-radius: 8px;
}

.search-icon {
  cursor: pointer;
  padding: 9px 12px;
}

.search-input {
  width: 0;
  transition: width 0.3s;
}

.search-input.expanded {
  width: 200px;
}

.segmentation-list {
  flex: 1;
  // min-height: 0;
  // margin-right: -16px;
  // padding-right: 16px;
  // overflow-y: auto;
  // overflow-x: hidden;
  overflow: hidden;
}
.segmentation-checkbox-group {
  font-size: inherit;
  line-height: inherit;
}

.segmentation-card {
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #dadde8;
  padding: 16px;
  display: flex;
  align-items: center;
  cursor: default;
}

.ell {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 显示行数 */
  overflow: hidden;
}
.no-border:deep(.el-textarea__inner) {
  // box-shadow: none;
  resize: none;
  min-height: 500px;
}
.scroller {
  height: 100%;
  padding-right: 16px;
}
</style>

<style lang="less">
.dataset-drawer-header {
  margin-bottom: 8px;
}

.highlight {
  background-color: #ffeb3b;
  color: #000;
  padding: 0 2px;
  border-radius: 2px;
}
</style>
