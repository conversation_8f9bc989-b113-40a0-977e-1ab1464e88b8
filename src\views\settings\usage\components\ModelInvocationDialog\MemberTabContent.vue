<script lang="ts" setup>
import { formatToken } from '@/utils/formatter'
import type { TokenUsageAggregatePageQueryBo } from '@/api/usage/types'
import { useTableSort } from '../../hooks/useTableSort'
import { useAggregatePage } from '../../hooks/useAggregatePage'
import dayjs from 'dayjs'
import { DeptVO } from '@/api/system/dept/types'

defineOptions({
  name: 'MemberTabContent',
})

// 定义组件的 props
const props = defineProps<{
  modelId: string
  timeRange: { startTime: string; endTime: string }
  deptOptions: DeptVO[]
}>()

// 姓名输入框的值
const nameInput = ref('')

// 选择部门
const selectedDept = ref<number>()

// 使用 useAggregatePage hook
const {
  tableRef,
  loading,
  tableData,
  currentPage: pageNum,
  pageSize,
  total,
  getTokenUsageAggregatePage,
} = useAggregatePage()

// 搜索功能
function handleSearch() {
  // 重置页码并获取数据
  pageNum.value = 1
  fetchTableData()
}

const { sortField, sortOrder, sortChange, resetSort } = useTableSort(handleSearch, tableRef)

// 获取表格数据
async function fetchTableData() {
  const nickName = nameInput.value?.trim()

  const params: TokenUsageAggregatePageQueryBo = {
    startTime: dayjs(props.timeRange.startTime).format('YYYY-MM-DD 00:00:00'),
    endTime: dayjs(props.timeRange.endTime).format('YYYY-MM-DD 23:59:59'),
    aggregateType: 'USER',
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    modelIds: [props.modelId],
    // 添加部门筛选条件
    deptIds: selectedDept.value ? [selectedDept.value] : undefined,
    nickNames: nickName ? [nickName] : [],
  }

  if (sortField.value && sortOrder.value) {
    params.sortBy = sortField.value
    params.sortOrder = sortOrder.value
  }

  await getTokenUsageAggregatePage(params)
}

// 重置搜索条件
function resetSearch() {
  nameInput.value = ''
  selectedDept.value = undefined
  pageNum.value = 1
  resetSort()
}

// 重置所有状态
function resetAll() {
  resetSearch()
  tableData.value = []
  total.value = 0
}

// 处理分页变化
function handleCurrentChange(val: number) {
  pageNum.value = val
  fetchTableData()
}

// 处理页面大小变化
function handleSizeChange(val: number) {
  pageSize.value = val
  pageNum.value = 1
  fetchTableData()
}

// 暴露方法给父组件
defineExpose({
  fetchTableData,
  resetSearch,
  resetAll,
  pageNum,
  pageSize,
})
</script>

<template>
  <div class="member-tab-content">
    <!-- 搜索区域 -->
    <div class="search-row">
      <div class="search-item">
        <el-input
          v-model="nameInput"
          placeholder="姓名"
          clearable
          class="search-input"
          @keyup.enter="handleSearch"
        />
      </div>
      <div class="search-item">
        <!-- <el-select v-model="selectedDept" placeholder="选择部门" clearable class="search-select">
          <el-option
            v-for="item in deptOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select> -->
        <el-tree-select
          v-model="selectedDept"
          :data="deptOptions"
          check-strictly
          placeholder="选择部门"
          class="search-select"
          :render-after-expand="false"
          default-expand-all
          clearable
        >
        </el-tree-select>
      </div>
      <div class="search-item">
        <el-button type="primary" plain @click="handleSearch" class="search-button">
          搜索
        </el-button>
      </div>
    </div>

    <!-- 表格区域 -->
    <el-table
      ref="tableRef"
      :data="tableData"
      @sort-change="sortChange"
      v-loading="loading"
      style="width: 100%; margin-top: 20px; min-height: 350px"
    >
      <el-table-column type="index" label="序号" width="80" align="center" />
      <el-table-column prop="entityName" label="姓名" show-overflow-tooltip />
      <el-table-column prop="groupName" label="部门" show-overflow-tooltip />
      <el-table-column prop="inputTokens" label="输入Tokens数" sortable="custom">
        <template #default="{ row }">
          <span>{{ `${formatToken(row.inputTokens)}` }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="outputTokens" label="输出Tokens数" sortable="custom">
        <template #default="{ row }">
          <span>{{ `${formatToken(row.outputTokens)}` }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalTokens" label="调用总量Tokens数" sortable="custom">
        <template #default="{ row }">
          <span>{{ `${formatToken(row.totalTokens)}` }}</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageNum"
      :page-sizes="[10]"
      :page-size="pageSize"
      :total="total"
      layout="total, prev, pager, next, jumper"
      style="margin-top: 20px; justify-content: center"
    />
  </div>
</template>

<style lang="less" scoped>
.search-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input,
.search-select {
  width: 200px;
}

.search-button {
  height: 40px;
}
</style>
