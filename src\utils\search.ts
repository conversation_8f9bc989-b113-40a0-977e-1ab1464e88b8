/**
 * 根据搜索查询过滤列表
 * @param items 列表项数组
 * @param searchQuery 搜索查询
 * @param labelField 用于搜索的字段名，默认为 'label'
 * @returns 过滤后的列表项
 */
export function filterListBySearchQuery<T extends Record<string, any>>(
  items: T[],
  searchQuery: string,
  labelField: string = 'label',
) {
  if (!searchQuery) return items
  const query = searchQuery.toLowerCase()
  const list = items.filter(item =>
    item[labelField] &&
    item[labelField].toString().toLowerCase().includes(query)
  )
  return list
}

export function handleGroupList<T extends Record<string, any>>(
  items: T[],
  groupBy?: string,
) {
  if (!items || items.length === 0) return []
  if (!groupBy) {
    return [{
      groupName: groupBy,
      list: items
    }]
  }

  const flatList = items.map(item => item[groupBy])
  const uniqueList = flatList.filter(
    (item, index) => flatList.indexOf(item) === index,
  )

  return uniqueList.map(item => ({
    groupName: item,
    list: items.filter(item2 => item2[groupBy] === item),
  }))
}