<script setup lang="ts">
import { ref, watch, type Ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getBizAiModelAvailableModels, postBizAiModelSaveDefault } from '@/api'
import { useSettingStore } from '@/store'
import '@/typings/model.d.ts'
// import Selector from './Selector.vue'
defineOptions({
  name: 'DefaultSettingsPopover',
})

interface AiModel {
  id: number
  displayName: string
  name: string // 对应 AIModelVo 中的 name
  type?: string // 对应 AIModelVo 中的 type，使用 string 以兼
}

const visible = ref(false)
const settingStore = useSettingStore()
// 设置参数
const defaultSettings = ref<Model.ModelDefault>({
  chatModelId: undefined,
  embeddingModelId: undefined,
  rerankModelId: undefined,
  summaryModelId: undefined,
})

// 模型列表
const chatModels = ref<AiModel[]>([])
const embeddingModels = ref<AiModel[]>([])
const rerankModels = ref<AiModel[]>([])
const summaryModels = ref<AiModel[]>([])
const loading = ref(false) // 全局加载状态
const chatModelLoading = ref(false)
const embeddingModelLoading = ref(false)
const rerankModelLoading = ref(false)
const summaryModelLoading = ref(false)

// 异步操作包装函数，用于处理加载状态和错误
const withLoadingAndErrorHandling = async (
  action: () => Promise<any>,
  loadingRef: Ref<boolean>,
  errorMessage: string,
) => {
  loadingRef.value = true
  try {
    await action()
  } catch (error) {
    console.error(errorMessage, error)
    ElMessage.error(errorMessage)
  } finally {
    loadingRef.value = false
  }
}

// 加载默认模型配置
const fetchDefaultSettings = async () => {
  const modelData = await settingStore.fetchDefaultModel(true)
  if (modelData) {
    defaultSettings.value = {
      chatModelId: modelData.chatModelId,
      embeddingModelId: modelData.embeddingModelId,
      rerankModelId: modelData.rerankModelId,
      summaryModelId: modelData.summaryModelId,
    }
  }
}

// 加载各类型可用模型
const fetchAvailableModels = async () => {
  const results = await Promise.allSettled([
    getBizAiModelAvailableModels({ type: 'text' }),
    getBizAiModelAvailableModels({ type: 'embedding' }),
    getBizAiModelAvailableModels({ type: 'rerank' }),
  ])

  console.log('获取可用模型结果:', results)

  // 处理 chatModels
  if (results[0].status === 'fulfilled') {
    const list = (results[0].value.data || []).map(item => ({
      ...item,
      label: item.displayName,
      value: item.id,
      modelName: item.name,
    }))

    chatModels.value = list
    summaryModels.value = list // 话题命名使用文本模型
  } else {
    console.error('获取对话模型失败:', results[0].reason)
    ElMessage.error('获取对话模型失败')
  }

  // 处理 embeddingModels
  if (results[1].status === 'fulfilled') {
    const list = (results[1].value.data || []).map(item => ({
      ...item,
      label: item.displayName,
      value: item.id,
      modelName: item.name,
    }))
    embeddingModels.value = list
  } else {
    console.error('获取文本向量模型失败:', results[1].reason)
    ElMessage.error('获取文本向量模型失败')
  }

  // 处理 rerankModels
  if (results[2].status === 'fulfilled') {
    const list = (results[2].value.data || []).map(item => ({
      ...item,
      label: item.displayName,
      value: item.id,
      modelName: item.name,
    }))
    rerankModels.value = list
  } else {
    console.error('获取重排序模型失败:', results[2].reason)
    ElMessage.error('获取重排序模型失败')
  }
  // checkDefaultModelSettings()
}

/**
 * 加载所有模型数据，包括默认配置和可用模型列表。
 * 该函数会设置全局加载状态，并处理数据加载过程中的错误。
 */
const loadModelData = async () => {
  loading.value = true // 开启全局加载状态
  try {
    // 异步加载默认设置和可用模型列表
    await Promise.all([
      withLoadingAndErrorHandling(fetchDefaultSettings, loading, '获取默认配置失败'),
      withLoadingAndErrorHandling(fetchAvailableModels, loading, '获取可用模型列表失败'),
    ])
  } catch (error) {
    // 这里的catch主要捕获Promise.all的错误，但由于withLoadingAndErrorHandling内部已处理，此处可能不触发
    console.error('加载模型数据失败', error)
    ElMessage.error('加载模型数据失败')
  } finally {
    loading.value = false // 关闭全局加载状态
  }
}

// 取消设置
const cancelSetting = () => {
  visible.value = false
}

// 保存设置
const saveSetting = async () => {
  loading.value = true
  try {
    await postBizAiModelSaveDefault(defaultSettings.value)
    checkDefaultModelSettings()
    ElMessage.success('保存成功')
    visible.value = false
  } catch (error) {
    console.error('保存配置失败', error)
    ElMessage.error('保存配置失败')
  } finally {
    loading.value = false
  }
}

// 监听visible变化
watch(visible, newVal => {
  if (newVal) {
    loadModelData()
  }
})

const hasUnconfiguredModels = ref(false)
// 检查默认模型配置状态
const checkDefaultModelSettings = async () => {
  try {
    const defaultSettings = await settingStore.fetchDefaultModel(false) // 不再强制刷新，依赖 store 内部缓存逻辑
    // const chatModelId = defaultSettings?.chatModelId
    // const isExistChatModel = chatModels.value.some(
    //   model => String(model.id) === String(chatModelId),
    // )
    // if (!isExistChatModel) {
    //   hasUnconfiguredModels.value = true
    //   return
    // }
    // const embeddingModelId = defaultSettings?.embeddingModelId
    // const isExistEmbeddingModel = embeddingModels.value.some(
    //   model => String(model.id) === String(embeddingModelId),
    // )
    // if (!isExistEmbeddingModel) {
    //   hasUnconfiguredModels.value = true
    //   return
    // }
    // const rerankModelId = defaultSettings?.rerankModelId
    // const isExistRerankModel = rerankModels.value.some(
    //   model => String(model.id) === String(rerankModelId),
    // )
    // if (!isExistRerankModel) {
    //   hasUnconfiguredModels.value = true
    //   return
    // }
    // const summaryModelId = defaultSettings?.summaryModelId
    // const isExistSummaryModel = summaryModels.value.some(
    //   model => String(model.id) === String(summaryModelId),
    // )
    // if (!isExistSummaryModel) {
    //   hasUnconfiguredModels.value = true
    //   return
    // }

    // 检查是否有未配置的模型
    hasUnconfiguredModels.value =
      !defaultSettings?.chatModelId ||
      !defaultSettings?.embeddingModelId ||
      !defaultSettings?.rerankModelId ||
      !defaultSettings?.summaryModelId
  } catch (error) {
    console.error('获取默认模型配置失败:', error)
    hasUnconfiguredModels.value = true
  }
}
checkDefaultModelSettings()
</script>

<template>
  <el-popover
    :visible="visible"
    trigger="click"
    placement="bottom-end"
    :width="360"
    popper-class="kb-settings-popover-popper"
    @hide="visible = false"
  >
    <template #reference>
      <slot>
        <el-badge v-if="hasUnconfiguredModels" class="ml-3" value="!" type="danger">
          <el-button type="primary" :disabled="loading" plain @click="visible = !visible"
            >系统默认模型设置</el-button
          >
        </el-badge>
        <el-button v-else type="primary" :disabled="loading" plain @click="visible = !visible"
          >系统默认模型设置</el-button
        >
      </slot>
    </template>

    <div class="kb-settings-popover">
      <!-- 设置选项 -->
      <div v-loading="loading" class="settings-list">
        <div class="search-text-config-item vertical">
          <div class="config-label-row">
            <span class="search-text-config-label">对话模型</span>
          </div>
          <div class="config-control-row full-width">
            <Selector
              v-model="defaultSettings.chatModelId"
              :loading="chatModelLoading"
              :opentionList="chatModels"
              placeholder="请选择模型"
            />
            <!-- <el-select
              v-model="defaultSettings.chatModelId"
              placeholder="请选择对话模型"
              class="model-select"
              clearable
              :loading="chatModelLoading"
            >
              <template #prefix>
                <img class="size-[20px]" src="@/assets/knowledge/model-icon.png" alt="" />
              </template>
              <el-option
                v-for="model in chatModels"
                :key="model.id"
                :label="model.displayName"
                :value="model.id"
              />
            </el-select> -->
          </div>
        </div>
        <div class="search-text-config-item vertical">
          <div class="config-label-row">
            <span class="search-text-config-label">话题命名模型</span>
          </div>
          <div class="config-control-row full-width">
            <Selector
              v-model="defaultSettings.summaryModelId"
              :loading="summaryModelLoading"
              :opentionList="summaryModels"
              placeholder="请选择模型"
            />
            <!-- <el-select
              v-model="defaultSettings.summaryModelId"
              placeholder="请选择话题命名模型"
              class="model-select"
              clearable
              :loading="summaryModelLoading"
            >
              <template #prefix>
                <img class="size-[20px]" src="@/assets/knowledge/model-icon.png" alt="" />
              </template>
              <el-option
                v-for="model in summaryModels"
                :key="model.id"
                :label="model.displayName"
                :value="model.id"
              />
            </el-select> -->
          </div>
        </div>
        <div class="search-text-config-item vertical">
          <div class="config-label-row">
            <span class="search-text-config-label">文本向量模型</span>
          </div>
          <div class="config-control-row full-width">
            <Selector
              v-model="defaultSettings.embeddingModelId"
              :loading="embeddingModelLoading"
              :opentionList="embeddingModels"
              placeholder="请选择模型"
            />
            <!-- <el-select
              v-model="defaultSettings.embeddingModelId"
              placeholder="请选择文本向量模型"
              class="model-select"
              clearable
              :loading="embeddingModelLoading"
            >
              <template #prefix>
                <img class="size-[20px]" src="@/assets/knowledge/model-icon.png" alt="" />
              </template>
              <el-option
                v-for="model in embeddingModels"
                :key="model.id"
                :label="model.displayName"
                :value="model.id"
              />
            </el-select> -->
          </div>
        </div>
        <div class="search-text-config-item vertical">
          <div class="config-label-row">
            <span class="search-text-config-label">重排序模型</span>
          </div>
          <div class="config-control-row full-width">
            <Selector
              v-model="defaultSettings.rerankModelId"
              :loading="rerankModelLoading"
              :opentionList="rerankModels"
              placeholder="请选择模型"
            />
            <!-- <el-select
              v-model="defaultSettings.rerankModelId"
              placeholder="请选择重排序模型"
              class="model-select"
              clearable
              :loading="rerankModelLoading"
            >
              <template #prefix>
                <img class="size-[20px]" src="@/assets/knowledge/model-icon.png" alt="" />
              </template>
              <el-option
                v-for="model in rerankModels"
                :key="model.id"
                :label="model.displayName"
                :value="model.id"
              />
            </el-select> -->
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="footer flex justify-between">
        <el-button
          plain
          class="w-[50%]"
          type="primary"
          size="large"
          :disabled="loading"
          @click="cancelSetting"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          class="w-[50%]"
          size="large"
          :loading="loading"
          @click="saveSetting"
        >
          保存
        </el-button>
      </div>
    </div>
  </el-popover>
</template>

<style scoped lang="less">
.kb-settings-popover {
  padding: 0;
  border-radius: 8px;
  background: #fff;
}

.kb-settings-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kb-settings-header .title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  align-items: center;
}

.close-icon {
  cursor: pointer;
  font-size: 16px;
  color: #909399;
  &:hover {
    color: #606266;
  }
}

.settings-list {
  padding: 16px;
  max-height: 360px;
  overflow-y: auto;
}

.search-text-config-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  &.vertical {
    flex-direction: column;
    align-items: flex-start;
  }
}

.config-label-row {
  margin-bottom: 8px;
  width: 100%;
}

.config-control-row {
  display: flex;
  align-items: center;
  width: 100%;

  &.full-width {
    display: block;
  }
}

.search-text-config-label {
  font-size: 14px;
  color: #303133;
}

.slider {
  flex: 1;
  margin-right: 16px;
}

.slider-input {
  width: 100px;
}

.model-select {
  width: 100%;
}

.slider-input {
  width: 120px;
  height: 44px;
  background: #ffffff;
  margin-left: 24px;

  :deep(.el-input__inner) {
    text-align: left;
  }
  :deep(.el-input-number__decrease),
  :deep(.el-input-number__increase) {
    --el-input-number-controls-height: 20px;
    --el-border-radius-base: 8px;
    background: #ffffff;
  }
}

.footer {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.dark {
  .kb-settings-popover {
    background: #1f1f1f;
  }

  .kb-settings-header {
    border-bottom: 1px solid #2d2d2d;
  }

  .kb-settings-header .title {
    color: #e5eaf3;
  }

  .close-icon {
    color: #a3a6ad;
    &:hover {
      color: #e5eaf3;
    }
  }

  .search-text-config-label {
    color: #e5eaf3;
  }

  .footer {
    border-top: 1px solid #2d2d2d;
  }
}
</style>

<style>
.kb-settings-popover-popper {
  padding: 0 !important;
}
</style>
