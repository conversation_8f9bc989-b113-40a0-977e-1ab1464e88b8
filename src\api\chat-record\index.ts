import { post, get } from '@/utils/request'
import type { ChatVo, UpdateChatParams, ChatRecordItem, GetChatRecordParams, ChatRecordListParams, ChatRecordListResultModel } from './type'

/**
 * 更新对话
 * @param data 对话数据
 */
export function updateChat(data: UpdateChatParams) {
  return post<ChatVo>({
    url: '/biz/chat/update',
    data
  })
}


/**
 * 获取对话记录
 */
export function getChatRecord(data: GetChatRecordParams) {
  return get<ChatRecordItem[]>({
    url: '/biz/chat/getChatRecord',
    data
  })
}

/**
 * 查询对话记录
 */
export function queryConversationRecord(data: ChatRecordListParams) {
  return get<{ rows: ChatRecordListResultModel[] }>({
    url: '/biz/conversation/inner/queryRecord',
    data
  })
}