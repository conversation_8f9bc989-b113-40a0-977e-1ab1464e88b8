<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { NButton } from 'naive-ui'
import { Message, ShareDialog, Skeleton } from './components'
import { useScroll } from './hooks/useScroll'
import { useChat } from './hooks/useChat'
import { useUsingContext } from './hooks/useUsingContext'
import { useModelManagement } from './hooks/useModelManagement'
import { useConversationDetail } from './hooks/useConversationDetail'
import { useChatRecords } from './hooks/useChatRecords'
import { useConversationActions } from './hooks/useConversationActions'
import MobileHeaderComponent from './components/MobileHeader/index.vue'
import SettingSider from './components/SettingSider/index.vue'
// import { usePublicChat } from './hooks/usePublicChat'
import { HoverButton, SvgIcon } from '@/components/common'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useChatStore, usePromptStore, useUserStore } from '@/store'
import { t } from '@/locales'
import { useAppStore } from '@/store/modules/app'
import { setRequestAgentContext } from '@/utils/token-manager'
const route = useRoute()
const router = useRouter()

function useHeaderActions(headComp: Component) {
  const headerActions = inject<Ref<any>>('headerActions')
  onMounted(() => {
    if (headerActions) {
      headerActions.value = h(headComp)
    }
  })
  onUnmounted(() => {
    if (headerActions) {
      headerActions.value = null
    }
  })
}

useHeaderActions(h(ElButton, { onClick: () => router.back() }, '返回'))
// const { isPublicChat } = usePublicChat()
const isPublicChat = false

// 从路由获取会话ID和助手ID
const { conversationId } = route.params as { conversationId: string }
const releaseId = computed(() => route.query.releaseId as string)

// releaseId的处理通过watch来完成，确保响应式更新

// 基础状态和布局
const { isMobile } = useBasicLayout()
const chatStore = useChatStore()
const appStore = useAppStore()

// 使用钩子函数
const {
  scrollRef,
  scrollToBottom,
  scrollToBottomIfAtBottom,
  isUserNearBottom,
  maintainScrollPosition,
} = useScroll()
const { usingContext } = useUsingContext()
const { updateChatSome } = useChat()
const { modelList, currentModel, fetchModelList, handleModelChange } =
  useModelManagement(conversationId)
const { conversationDetail, kbList, fetchConversationDetail } = useConversationDetail()
const {
  latestChatId,
  hasMoreChatRecords,
  isLoadingMoreRecords,
  initialLoadComplete,
  fetchChatRecord,
  loadMoreChatRecords,
} = useChatRecords(conversationId, scrollToBottom, maintainScrollPosition)
const { handleClear, loading, handleStop, setController, restoreConversationState } =
  useConversationActions(conversationId, scrollToBottomIfAtBottom, scrollToBottom)

// UI状态变量
const showShareDialog = ref(false)
const settingSiderCollapsed = ref(false)
const showMobileSettingSider = ref(false)
const showNewContext = ref(false) // 显示"新的上下文"提示
const isWidthLimited = computed(() => appStore.isWidthLimited)

// 数据源和计算属性
const dataSources = computed(() => {
  const data = chatStore.getChatByConversationId(conversationId)
  // 过滤掉undefined或不完整的项目
  return data.filter(item => item && typeof item === 'object' && 'dateTime' in item)
})

// 计算属性
const lastQuestionFromHistory = computed(() => {
  const currentHistory = chatStore.getChatHistoryByCurrentActive
  return currentHistory?.lastQuestion
})

// 检查是否没有消息
const hasNoLastQuestion = computed(() => {
  // 优先从history中获取lastQuestion
  const hasLastQuestion = lastQuestionFromHistory.value || conversationDetail.value?.lastQuestion
  // 检查dataSources中是否有消息，如果有至少一条消息，则不显示空白界面
  return !hasLastQuestion && dataSources.value.length === 0
})

// 获取当前聊天标题
const currentChatTitle = computed(() => {
  const current = chatStore.history.find(item => item.conversationId === conversationId)
  return current?.title || t('chat.newChatTitle')
})

// 获取系统消息
const currentSystemMessage = computed(() => {
  // 首先从history中获取，因为那里包含了从服务器获取的systemMessage
  const currentHistory = chatStore.getChatHistoryByCurrentActive
  if (currentHistory?.systemMessage) return currentHistory.systemMessage
  return ''
})

// 计算模型配置，避免每次渲染都创建新对象
const modelConfig = computed(() => ({
  temperature: conversationDetail.value?.temperature,
  maxContextCount: conversationDetail.value?.maxContextCount,
  singleReplyLimitFlag: conversationDetail.value?.singleReplyLimitFlag,
  maxTokens: conversationDetail.value?.maxTokens,
}))

function toggleMobileSettingSider() {
  showMobileSettingSider.value = !showMobileSettingSider.value
}

// 处理聊天宽度限制切换
function toggleChatWidth() {
  appStore.setIsWidthLimited(!appStore.isWidthLimited)
}

// 处理分享弹窗
function handleShare() {
  showShareDialog.value = true
}

// 生命周期钩子
onMounted(async () => {
  if (!conversationId) return
  try {
    await fetchConversationDetail(conversationId) // 先获取会话详情
    // 确保chatStore中有当前会话数据
    if (!chatStore.getChatHistoryByCurrentActive) {
      // 等待数据加载完成
      await new Promise<void>(resolve => {
        // 设置一个最长等待时间
        const timeout = setTimeout(() => resolve(), 3000)

        // 直接使用setInterval检查
        const interval = setInterval(() => {
          if (chatStore.getChatHistoryByCurrentActive) {
            clearInterval(interval)
            clearTimeout(timeout)
            resolve()
          }
        }, 100)
      })
    }

    // 恢复会话状态（如果当前会话正在进行流式响应）
    const restoredState = restoreConversationState()

    if (!restoredState.isLoading) {
      await fetchChatRecord(false, currentModel.value) // 再获取聊天记录 此处需要用到ConversationDetail里的modelId
    } else {
      console.log(`会话 ${conversationId} 正在流式响应中，跳过fetchChatRecord`)
      initialLoadComplete.value = true
    }

    await fetchModelList(conversationDetail.value) // 获取模型列表

    // 初始化显示"新的上下文"提示为false
    showNewContext.value = false

    // 为每个请求创建新的controller
    setController(new AbortController())

    // 未知原因刷新页面，loading 状态不会重置，手动重置
    dataSources.value.forEach((item, index) => {
      if (item.loading) updateChatSome(conversationId, index, { loading: false })
    })
  } catch (error) {
    console.error('初始化聊天界面失败:', error)
  }
})

// 处理图片加载完成事件
function handleImageLoad() {
  // 图片加载完成后，如果用户在底部附近，重新滚动到底部
  if (isUserNearBottom.value) {
    scrollToBottom()
  }
}

onUnmounted(() => {
  // 不在组件卸载时自动停止会话，让会话能够在后台持续运行
  // 清理DOM事件监听器
})

// 监听releaseId变化，重新设置全局上下文
watch(
  releaseId,
  newReleaseId => {
    if (isPublicChat && newReleaseId) {
      setRequestAgentContext(newReleaseId)
      console.log('Chat页面 - releaseId变化:', newReleaseId)
    } else if (!newReleaseId) {
      // 如果releaseId为空，清除助手上下文，使用默认用户Store
      setRequestAgentContext(null)
      console.log('Chat页面 - 清除releaseId上下文')
    }
  },
  { immediate: true },
)

// 监听 dataSources 变化，自动滚动（特别是在后台流式响应时）
watch(
  dataSources,
  (newData, oldData) => {
    // 如果当前会话正在loading，并且数据有更新，自动滚动
    if (loading.value && newData.length > 0) {
      const lastMessage = newData[newData.length - 1]
      if (lastMessage && lastMessage.loading) {
        // 使用nextTick确保DOM更新后再滚动
        nextTick(() => {
          scrollToBottomIfAtBottom()
        })
      }
    }
  },
  { deep: true, flush: 'post' },
)
</script>

<template>
  <div class="flex flex-col h-full bg-[#F7F8FA]">
    <header
      v-if="!isMobile"
      class="flex justify-between items-center p-4 border-b dark:border-neutral-800"
    >
      <div class="flex items-center gap-2">
        <img src="@/assets/logo-gray.png" alt="" class="w-[48px] h-[48px]" />
        <h2 class="text-lg dark:text-white font-bold" style="font-size: 20px">
          {{ currentChatTitle }}
        </h2>
      </div>
      <div class="flex gap-2">
        <!-- resolved 增加一个按钮，点击能够让chat主体内容宽度限制为960px -->
        <HoverButton @click="toggleChatWidth">
          <span class="text-xl text-[#4f555e] dark:text-white">
            <SvgIcon
              style="font-size: 24px"
              :icon="
                isWidthLimited ? 'mdi:arrow-expand-horizontal' : 'mdi:arrow-collapse-horizontal'
              "
            />
          </span>
        </HoverButton>
        <!-- resolved 点击弹出一个分享弹窗el-dialog，默认生成一个图片，图片内容为当前聊天记录，图片下方有下载按钮 -->
        <!-- resolved 点击这个按钮时候，先出现复选框能让用户勾选需要分享的聊天记录，再对应生成图片 -->
        <HoverButton @click="handleShare">
          <span class="text-xl text-[#4f555e] dark:text-white">
            <SvgIcon icon="material-symbols:share-outline" />
          </span>
        </HoverButton>
      </div>
    </header>

    <div class="flex flex-1 overflow-hidden relative">
      <div
        class="flex-1 flex flex-col overflow-hidden"
        :class="{ 'pr-[360px]': !settingSiderCollapsed && !isMobile }"
      >
        <MobileHeaderComponent
          v-if="isMobile"
          :using-context="usingContext"
          :show-right-sider="showMobileSettingSider"
          @handle-clear="
            () => {
              const result = handleClear(showNewContext, latestChatId)
              if (result?.then) {
                result.then(res => {
                  if (res) {
                    showNewContext = res.showNewContext
                    latestChatId = res.latestChatId
                  }
                })
              }
            }
          "
          @toggle-right-sider="toggleMobileSettingSider"
        />
        <main class="flex-1 overflow-hidden relative">
          <div id="scrollRef" ref="scrollRef" class="absolute inset-0 overflow-y-auto">
            <div
              id="image-wrapper"
              class="w-full m-auto dark:bg-[#101014] bg-[#F7F8FA]"
              :class="[
                isMobile ? 'p-2 max-w-screen-xl' : 'p-4',
                { 'max-w-[960px]': isWidthLimited && !isMobile },
              ]"
            >
              <div
                class="relative"
                :class="{ 'max-w-[960px] mx-auto': isWidthLimited && !isMobile }"
              >
                <!-- 无数据状态 -->
                <div
                  v-if="hasNoLastQuestion"
                  class="flex items-center justify-center mt-4 text-center text-neutral-300"
                >
                  <SvgIcon icon="ri:bubble-chart-fill" class="mr-2 text-3xl" />
                  <span>{{ t('chat.newChatTitle') }}</span>
                </div>
                <!-- 添加数据加载过渡状态 -->
                <template v-else-if="!initialLoadComplete">
                  <div class="space-y-8">
                    <Skeleton v-for="i in 2" :key="i" />
                  </div>
                </template>
                <!-- 数据加载完成状态 -->
                <template v-else>
                  <!-- 加载更多按钮 -->
                  <div v-if="hasMoreChatRecords" class="flex justify-center py-4">
                    <NButton
                      style="color: #999"
                      size="small"
                      :loading="isLoadingMoreRecords"
                      @click="loadMoreChatRecords(currentModel)"
                    >
                      {{ isLoadingMoreRecords ? '加载中...' : '加载更多历史消息' }}
                    </NButton>
                  </div>
                  <div class="chat-messages-container">
                    <Message
                      v-for="(item, index) of dataSources"
                      :key="index"
                      :latest-chat-id="latestChatId"
                      :date-time="item?.dateTime || ''"
                      :text="item?.text || ''"
                      :inversion="item?.inversion || false"
                      :error="item?.error || false"
                      :loading="item?.loading || false"
                      :can-retry="false"
                      :index="index"
                      :previous-chat-id="
                        index > 0 ? dataSources[index - 1]?.conversationOptions?.chatId : undefined
                      "
                      :current-chat-id="item?.conversationOptions?.chatId"
                      :tool-execution-list="item?.toolExecutionList"
                      :chat-content-record-list="item?.chatContentRecordList"
                      :reasoning-content="item?.reasoningContent"
                      :image-info-list="item?.imageInfoList"
                      :on-image-load="handleImageLoad"
                      readonly
                    />
                    <!-- 新上下文提示 -->
                    <div v-if="showNewContext" class="flex items-center justify-center my-4">
                      <div class="w-[100px] border-t border-gray-200 dark:border-gray-600" />
                      <span class="mx-4 text-xs text-gray-400 dark:text-gray-500">新的上下文</span>
                      <div class="w-[100px] border-t border-gray-200 dark:border-gray-600" />
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <!-- 停止响应按钮 -->
          <div class="stop-loading-btn">
            <NButton
              v-show="loading"
              style="border-radius: 8px"
              secondary
              type="primary"
              @click="handleStop"
            >
              <template #icon>
                <SvgIcon icon="ri:stop-circle-line" />
              </template>
              <span>停止响应</span>
            </NButton>
          </div>
          <!-- 滚动到底部按钮 -->
          <div v-show="dataSources.length && !isUserNearBottom" class="scroll-to-bottom-btn">
            <NButton secondary circle type="primary" @click="scrollToBottom">
              <template #icon>
                <SvgIcon icon="ri:arrow-down-line" />
              </template>
            </NButton>
          </div>
        </main>
      </div>
      <div
        v-show="!isMobile || showMobileSettingSider"
        class="absolute right-0 h-full transition-transform duration-300"
        :class="[isMobile ? 'w-full bg-white dark:bg-black z-50' : '']"
      >
        <SettingSider
          v-model:collapsed="settingSiderCollapsed"
          :mobile-mode="isMobile"
          :initial-system-message="currentSystemMessage"
          :initial-kb-list="kbList"
          :initial-model-config="modelConfig"
          readonly
          @close="showMobileSettingSider = false"
        />
      </div>
    </div>

    <!-- 分享对话框 -->
    <ShareDialog
      v-model:visible="showShareDialog"
      :conversation-id="conversationId"
      :chat-title="currentChatTitle"
      :model-name="currentModel"
      :model-list="modelList"
      :chat-messages="
        dataSources.map((item, index) => ({
          index,
          text: item.text,
          inversion: item.inversion,
          dateTime: item.dateTime,
        }))
      "
    />
  </div>
</template>

<style scoped lang="less">
.stop-loading-btn {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 1.5rem;
  z-index: 10;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s;

  &:hover {
    opacity: 1;
  }
}

.scroll-to-bottom-btn {
  position: absolute;
  right: 1.5rem;
  bottom: 1.5rem;
  z-index: 10;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.3s;

  &:hover {
    opacity: 1;
  }
}

.chat-messages-container {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
