/**
 * 根据旧分页信息，在 pageSize 改变后，计算新的页码
 * @param oldPage      旧的页码（从 1 开始）
 * @param oldPageSize  旧的每页数量
 * @param newPageSize  新的每页数量
 * @returns newPage    新的页码（从 1 开始）
 */
export function recalcPage(
  oldPage: number,
  oldPageSize: number,
  newPageSize: number
): number {
  if (oldPage < 1 || oldPageSize <= 0 || newPageSize <= 0) {
    throw new Error("参数不合法");
  }

  // 旧页的第一个数据序号
  const startIndex = (oldPage - 1) * oldPageSize + 1;

  // 计算新页码：index 映射到新 page
  const newPage = Math.floor((startIndex - 1) / newPageSize) + 1;

  return newPage;
}
