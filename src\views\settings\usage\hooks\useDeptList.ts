import { DeptVO } from "@/api/system/dept/types"
import { listDept } from '@/api/system/dept'
import { handleTree } from '@/utils/ruoyi'


export function useDeptList() {
  const deptOptions = ref<DeptVO[]>([])
  async function getDeptList() {
    try {
      deptOptions.value = []
      const res = await listDept()
      res.data.forEach((item: any) => {
        item.value = item.deptId
        item.label = item.deptName
      })
      if (res.data) {
        // 使用handleTree处理数据，转换为树形结构
        deptOptions.value = handleTree<DeptVO>(res.data, 'value', 'parentId', 'children')
      }
    } catch (error) {
      console.error('获取部门列表失败:', error)

    }
  }

  return {
    deptOptions,
    getDeptList
  }
}