<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue'
import { formatToken } from '@/utils/formatter'
import type { TokenUsageAggregatePageQueryBo, AggregateDataPoint } from '@/api/usage/types'
import { getAiPlatformList } from '@/api/model-base'
import { PlatformInfo } from '@/api/model-base/types'
import { useTableSort } from '../hooks/useTableSort'
import { useAggregatePage } from '../hooks/useAggregatePage'
import { getModelLogo } from '@/utils/models'
import dayjs from 'dayjs'

defineOptions({
  name: 'MemberInvocationDialog',
})

// 控制对话框显示/隐藏
const dialogVisible = defineModel({ default: false })

// 选择平台（多选）
const selectedPlatforms = ref<number[]>([])

// 模型名称输入框的值
const modelNameInput = ref('')

// 平台选项数据
const platformOptions = ref<{ value: number; label: string }[]>([])

// 获取平台选项数据
async function fetchPlatformOptions() {
  try {
    // 设置一个较大的pageSize以尽量减少请求次数
    const pageSize = 1000
    let pageNum = 1
    let platformList: PlatformInfo[] = []
    let hasMore = true

    // 循环获取所有数据
    while (hasMore) {
      const res = await getAiPlatformList()
      if (res.code === 200) {
        platformList = [...platformList, ...res.rows]
        // 如果当前页的数据少于pageSize，说明已经到最后一页了
        hasMore = res.total > pageSize
        pageNum++
      } else {
        console.error('获取平台列表失败:', res)
        hasMore = false
      }
    }

    // 将获取到的数据转换为下拉框需要的格式
    platformOptions.value = [
      ...platformList.map((item: any) => ({
        value: item.id,
        label: item.platformName,
      })),
    ]
  } catch (error) {
    console.error('获取平台列表失败:', error)
  }
}

// 编辑数据相关
const info = ref<Partial<AggregateDataPoint>>({})
const userId = computed(() => info.value.entityId)
const userName = computed(() => info.value.entityName)
const deptName = computed(() => info.value.groupName)
const inputTokens = computed(() => info.value.inputTokens || 0)
const outputTokens = computed(() => info.value.outputTokens || 0)
const totalTokens = computed(() => info.value.totalTokens || 0)
const timeRange = ref({ startTime: '', endTime: '' })

// 使用 useAggregatePage hook
const {
  tableRef,
  loading,
  tableData,
  currentPage: pageNum,
  pageSize,
  total,
  getTokenUsageAggregatePage,
} = useAggregatePage()

// 编辑方法，接收模型名称、平台名称、时间、输入tokens、输出tokens、调用总量tokens
function editData(dataPoint: AggregateDataPoint, startTime: string, endTime: string) {
  info.value = dataPoint
  timeRange.value = { startTime, endTime }
  openDialog()
}

// 打开对话框的方法
function openDialog() {
  dialogVisible.value = true
}

// 关闭对话框的方法
function closeDialog() {
  dialogVisible.value = false
  // 重置数据到初始状态
  selectedPlatforms.value = []
  modelNameInput.value = ''
  timeRange.value = { startTime: '', endTime: '' }
  tableData.value = []
  total.value = 0
  pageNum.value = 1
  resetSort()
}

// 搜索功能
function handleSearch() {
  // 这里可以添加搜索逻辑
  // console.log('成员调用搜索:', selectedPlatforms.value, modelNameInput.value)
  // 重置页码并获取数据
  pageNum.value = 1
  fetchTableData()
}

const { sortField, sortOrder, sortChange, resetSort } = useTableSort(handleSearch, tableRef)

// 获取表格数据
async function fetchTableData() {
  const modelName = modelNameInput.value?.trim()
  const params: TokenUsageAggregatePageQueryBo = {
    startTime: dayjs(timeRange.value.startTime).format('YYYY-MM-DD 00:00:00'),
    endTime: dayjs(timeRange.value.endTime).format('YYYY-MM-DD 23:59:59'),
    aggregateType: 'MODEL',
    userIds: [userId.value!],
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    modelNames: modelName ? [modelName] : [],
    platformIds: selectedPlatforms.value,
  }

  if (sortField.value && sortOrder.value) {
    params.sortBy = sortField.value
    params.sortOrder = sortOrder.value
  }

  await getTokenUsageAggregatePage(params)
}

watch(dialogVisible, val => {
  if (val) {
    fetchTableData()
  }
})

// 处理分页变化
function handleCurrentChange(val: number) {
  pageNum.value = val
  fetchTableData()
}

// 处理页面大小变化
function handleSizeChange(val: number) {
  pageSize.value = val
  pageNum.value = 1
  fetchTableData()
}

onMounted(() => {
  fetchPlatformOptions()
})

defineExpose({
  editData,
})
</script>

<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="950"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog"
    >
      <template #header>
        <span class="title g-family-medium">模型调用量明细</span>
      </template>
      <div class="model-invocation">
        <div class="model-invocation-header">
          <div class="model-invocation-name">
            <span v-if="deptName" class="g-family-medium">{{ deptName }}</span>
            <span v-if="deptName" class="mx-6">/</span>
            <span class="g-family-medium">{{ userName }}</span>
          </div>
          <div class="model-invocation-time">
            {{ timeRange.startTime }} 至 {{ timeRange.endTime }}
          </div>
        </div>
        <div class="model-invocation-content">
          <div>
            <div class="model-invocation-title">输入Tokens</div>
            <div class="model-invocation-num">{{ formatToken(inputTokens) }}</div>
          </div>
          <div>
            <div class="model-invocation-title">输出Tokens</div>
            <div class="model-invocation-num">{{ formatToken(outputTokens) }}</div>
          </div>

          <div>
            <div class="model-invocation-title">调用总量Tokens</div>
            <div class="model-invocation-num">{{ formatToken(totalTokens) }}</div>
          </div>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-row">
        <div class="search-item">
          <el-input
            v-model="modelNameInput"
            placeholder="模型名称"
            clearable
            class="search-input"
            @keyup.enter="handleSearch"
          />
        </div>
        <div class="search-item">
          <el-select
            v-model="selectedPlatforms"
            placeholder="选择平台"
            clearable
            multiple
            class="search-select"
          >
            <el-option
              v-for="item in platformOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <div class="search-item">
          <el-button type="primary" plain @click="handleSearch" class="search-button">
            搜索
          </el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        ref="tableRef"
        :data="tableData"
        @sort-change="sortChange"
        v-loading="loading"
        style="width: 100%; margin-top: 20px; min-height: 350px"
      >
        <el-table-column type="index" label="序号" width="80" align="center"> </el-table-column>
        <el-table-column prop="entityName" label="模型" show-overflow-tooltip>
          <template #default="{ row }">
            <img class="size-6 inline-block" :src="getModelLogo(row.entityName)" alt="模型logo" />
            {{ row.entityName }}
          </template>
        </el-table-column>
        <el-table-column prop="groupName" label="平台" show-overflow-tooltip />
        <el-table-column prop="inputTokens" label="输入Tokens数" sortable="custom">
          <template #default="{ row }">
            <span>{{ `${formatToken(row.inputTokens)}` }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="outputTokens" label="输出Tokens数" sortable="custom">
          <template #default="{ row }">
            <span>{{ `${formatToken(row.outputTokens)}` }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalTokens" label="调用总量Tokens数" sortable="custom">
          <template #default="{ row }">
            <span>{{ `${formatToken(row.totalTokens)}` }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="[10]"
        :page-size="pageSize"
        :total="total"
        layout="total, prev, pager, next, jumper"
        style="margin-top: 20px; justify-content: center"
      />
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.title {
  font-size: 16px;
  margin-bottom: 12px;
}
.model-invocation {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 28px;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-name {
    flex: 1;
    display: inline-flex;
    align-items: center;
    font-size: 18px;
    margin-bottom: 20px;
  }

  &-time {
    color: #646a73;
    font-size: 14px;
  }

  &-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }

  &-title {
    font-size: 16px;
    margin-bottom: 8px;
  }

  &-num {
    font-size: 28px;
    font-weight: 600;
    color: #4c5cec;
  }
}

.search-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input,
.search-select {
  width: 200px;
}

.search-button {
  height: 40px;
}
</style>
