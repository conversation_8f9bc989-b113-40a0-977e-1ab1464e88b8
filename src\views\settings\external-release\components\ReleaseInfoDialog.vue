<script setup lang="ts">
import { getOutwardUseStat } from '@/api/external-release'
import { OutwardVo, OutwardUseStat, OutwardType } from '@/api/external-release/type'
import webIcon from '@/assets/settings/external-release/web-icon.png'
import wechatBotIcon from '@/assets/settings/external-release/wechat-bot-icon.png'
import ddBotIcon from '@/assets/settings/external-release/dd-bot-icon.png'
import feishuBotIcon from '@/assets/settings/external-release/feishu-type-icon.png'
import spendTokensPng from '@/assets/settings/external-release/spend-tokens.png'
import { formatToken } from '@/utils/formatter'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { BarChart } from 'echarts/charts'
import { GridComponent, TooltipComponent, GraphicComponent } from 'echarts/components'
import { getOutwardDetail } from '@/api/external-release'
import { useUserStore } from '@/store/modules/user'
import { useAgentList } from '@/hooks/useAgentList'
import dayjs from 'dayjs'
const userStore = useUserStore()
const userInfo = userStore.userInfo
// 注册 ECharts 必要的组件
use([CanvasRenderer, BarChart, GridComponent, TooltipComponent, GraphicComponent])

defineOptions({
  name: 'ReleaseInfoDialog',
})

const emits = defineEmits(['updateReleaseList'])

const dialogVisible = defineModel({ default: false })
const outwardId = ref('')

/**
 * 默认最近7日
 */
const getLast7DaysRange = () => {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 6)

  const format = (date: Date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  return [format(startDate), format(endDate)]
}

const dateRange = ref(getLast7DaysRange())

function useOutwardDetail() {
  const detailData = ref<OutwardVo>()
  const loading = ref(false)

  function getOutwardInfo(id: string) {
    loading.value = true

    getOutwardDetail(id)
      .then(res => {
        if (res.code === 200) {
          detailData.value = res.data
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
  return {
    getOutwardInfo,
    detailData,
    loading,
  }
}

const { getOutwardInfo, detailData, loading: outwardInfoLoading } = useOutwardDetail()

/**
 * 应用发布类型的图标
 */
const publicationTypeIcon = computed(() => {
  switch (detailData.value?.outwardType.code) {
    case OutwardType.Web:
      return webIcon
    case OutwardType.Wechat:
      return wechatBotIcon
    case OutwardType.Dingtalk:
      return ddBotIcon
    case OutwardType.Feishu:
      return feishuBotIcon
    default:
      return ''
  }
})

function useStatDetail() {
  const statData = ref<OutwardUseStat[]>([])
  const loading = ref(false)

  const option = computed(() => {
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '8%',
        right: 0,
        bottom: 20,
      },
      xAxis: {
        type: 'category',
        data: filledStatData.value.map(item => formatDate(item.day)),
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: false,
        },
      },
      series: [
        {
          type: 'bar',
          data: filledStatData.value.map(item => item.useTokens || 0),
          itemStyle: {
            color: '#4865E8', // 设置柱状图颜色
            borderRadius: [4, 4, 4, 4], // 设置柱状图顶部圆角，[左上，右上，右下，左下]
          },
          barMaxWidth: 10, // 设置柱状图的最大宽度为 25px
        },
      ],
      //   graphic:
      //     statData.value.length === 0
      //       ? {
      //           type: 'text',
      //           left: 'center',
      //           top: 'middle',
      //           style: {
      //             text: '暂无数据',
      //             fill: '#969BA4',
      //             fontSize: 16,
      //           },
      //         }
      //       : null,
    }
  })

  function getStatInfo(id: string) {
    loading.value = true
    return getOutwardUseStat(id, {
      startDay: dateRange.value[0],
      endDay: dateRange.value[1],
    })
      .then(res => {
        if (res.code === 200) {
          statData.value = res.data
        }
      })
      .finally(() => {
        loading.value = false
      })
  }

  function formatDate(date?: string) {
    if (!date) return ''
    const d = new Date(date)
    return `${d.getMonth() + 1}-${d.getDate()}`
  }

  /**
   * 统计数据拿到的只是有值的数据
   * 根据统计时间 dateRange，将选择返回内的数据进行补全，没有使用的日期，默认为0
   */
  const filledStatData = computed(() => {
    if (!dateRange.value || !statData.value) return []

    const startDate = dayjs(dateRange.value[0])
    const endDate = dayjs(dateRange.value[1])
    const days = endDate.diff(startDate, 'day') + 1

    // 创建日期到数据的映射
    const dataMap = statData.value.reduce(
      (acc, item) => {
        const dateKey = dayjs(item.day).format('YYYY-MM-DD')
        acc[dateKey] = item.useTokens || 0
        return acc
      },
      {} as Record<string, number>,
    )

    // 生成完整日期序列并填充数据
    return Array.from({ length: days }, (_, i) => {
      const currentDate = startDate.add(i, 'day').format('YYYY-MM-DD')
      return {
        day: currentDate,
        useTokens: dataMap[currentDate] || 0,
      }
    })
  })

  return {
    statData,
    loading,
    getStatInfo,
    option,
  }
}

const { statData, getStatInfo, option } = useStatDetail()
function show(id: string) {
  outwardId.value = id
  getOutwardInfo(id)
  getStatInfo(id)
}

/**
 * 网页对话分享链接
 */
const shareUrl = computed(() => {
  return `${window.location.origin}/#/share/${detailData.value?.id}`
})

/**
 * 企微机器人分享链接
 */
const weChatWorkShareUrl = computed(() => {
  return `${window.location.origin}/prod-api/callback/wechat/chat/${detailData.value?.payload?.token}`
})

/**
 * 复制链接
 */
async function handleCopyLink() {
  try {
    let url = ''
    switch (detailData.value?.outwardType?.code) {
      case OutwardType.Web:
        url = shareUrl.value
        break
      case OutwardType.Wechat:
        url = weChatWorkShareUrl.value
        break
    }
    await navigator.clipboard.writeText(url)
    ElMessage.success('链接已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败，请手动复制')
  }
}

const { agentList, getAgentList } = useAgentList()

watchEffect(() => {
  if (dialogVisible.value) {
    getAgentList()
  }
})
/**
 * 当前选中的助手对象
 */
const selectedAgent = computed(() => {
  return agentList.value.find(item => item.id === detailData.value?.agentId)
})

/**
 * 计算 token 使用的进度条
 * 如果token 不限制的话，默认展示 30%
 */
const percentage = computed(() => {
  const tokens = detailData.value?.tokens ?? 0
  const maxTokens = detailData.value?.maxTokens

  if (maxTokens === -1) {
    return 30
  } else if (maxTokens === 0) {
    return tokens > 0 ? 100 : 0
  } else if (maxTokens !== undefined && tokens >= maxTokens) {
    return 100
  } else if (maxTokens === undefined) {
    // 如果 maxTokens 未定义，按不限制处理，返回默认值如 30%
    return 30
  } else {
    return Number(((tokens / maxTokens) * 100).toFixed(0))
  }
})

/**
 * 选择日期
 * 最多选择 30 天
 */
function handleDateChange(value: string[]) {
  if (value.length !== 2) return

  const startDate = dayjs(value[0])
  let endDate = dayjs(value[1])
  const diffDays = endDate.diff(startDate, 'day')

  if (diffDays > 29) {
    ElMessage.warning('最多只能选择30天内的日期范围')
    endDate = startDate.add(29, 'day')
  }

  dateRange.value[0] = startDate.format('YYYY-MM-DD')
  dateRange.value[1] = endDate.format('YYYY-MM-DD')
  getStatInfo(outwardId.value)
}

function handleBeforeClose(done: () => void) {
  done()
}

function handleClose() {
  dateRange.value = getLast7DaysRange()
  detailData.value = undefined
  statData.value = []
  dialogVisible.value = false
  outwardId.value = ''
}

defineExpose({
  show,
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="900"
    class="release-type-dialog"
    :before-close="handleBeforeClose"
    @close="handleClose"
  >
    <template #header>
      <span class="title g-family-medium">查看</span>
    </template>
    <div v-loading="outwardInfoLoading">
      <div>
        <div>
          <span class="title g-family-medium">{{ detailData?.name }}</span
          ><img
            v-if="publicationTypeIcon"
            class="inline-block w-[26px] ml-2"
            :src="publicationTypeIcon"
            alt=""
          />
        </div>
        <div class="text-[14px] text-[#646A73]">
          {{ detailData?.remark }}
        </div>
      </div>

      <!-- 网页对话 -->
      <div class="visit-info" v-if="detailData?.outwardType?.code === OutwardType.Web">
        <div class="visit-info-content">
          <div>{{ shareUrl }}</div>
          <div>分享人: {{ detailData?.createByName }}</div>
          <div>团队名称: {{ userInfo.tenantName }}</div>
          <div>链接有效期: {{ detailData?.createTime }}~{{ detailData?.deadline }}</div>
          <div>
            访问密码：{{ detailData?.payload.password ? detailData?.payload.password : '无' }}
          </div>
        </div>
        <div class="visit-info-btn web-btn" @click="handleCopyLink">复制 <br />链接</div>
      </div>

      <!-- 企微机器人 -->
      <div class="visit-info" v-if="detailData?.outwardType?.code === OutwardType.Wechat">
        <div class="visit-info-content">{{ weChatWorkShareUrl }}</div>
        <div class="visit-info-btn wechat-btn" @click="handleCopyLink">复制链接</div>
      </div>

      <div class="mt-6" v-if="selectedAgent">
        <div class="title g-family-medium">关联助手</div>
        <div class="agent-info w-full mb-6 mt-[-12px]">
          <div class="emoji-wrap" :style="{ backgroundColor: selectedAgent.backgroundColor }">
            <img class="emoji" :src="selectedAgent.emoji" alt="" />
          </div>
          <div v-if="selectedAgent" class="ml-4 flex-1">
            <div class="agent-info-name g-family-medium">
              {{ selectedAgent.name }}
            </div>
            <div class="agent-info-create-info">
              {{ selectedAgent.createUserName }}&nbsp;|&nbsp; {{ selectedAgent.createTime }}
            </div>
            <div class="agent-info-description">
              {{ selectedAgent.description }}
            </div>
          </div>
        </div>
      </div>

      <div class="mt-6">
        <div class="title g-family-medium">使用统计</div>
        <div class="use-stat">
          <div class="w-full flex items-center">
            <div class="w-[80px] mr-[24px]">
              <!-- <span class="module-icon iconfont icon-zhushou"></span> -->
              <img class="w-full" :src="spendTokensPng" alt="模型消耗" />
              <div class="text-[18px] text-center mt-[8px] g-family-medium">模型消耗</div>
            </div>
            <div class="flex-1 h-[140px] flex flex-col justify-between">
              <div class="flex items-center justify-between">
                <span class="text-[#4865E8] text-[16px]">已使用</span>
                <span class="text-[#969BA4] text-[16px]">max.</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="text-[#4865E8]">
                  <span class="text-[40px] font-600">{{
                    formatToken(detailData?.tokens || 0)
                  }}</span>
                  <span class="text-[24px] ml-2">Tokens</span>
                </div>

                <div>
                  <span class="text-[40px] font-600">{{
                    detailData?.maxTokens === -1 ? '∞' : formatToken(detailData?.maxTokens || 0)
                  }}</span>
                  <span class="text-[24px] ml-2">Tokens</span>
                </div>
              </div>
              <el-progress :percentage="percentage" :show-text="false" :stroke-width="8" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mt-6">
      <div class="text-right">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          @change="handleDateChange"
        />
      </div>
      <v-chart class="chart" :option="option" autoresize />
    </div>
  </el-dialog>
</template>

<style lang="less" scoped>
.title {
  font-size: 16px;
  margin-bottom: 12px;
}
.visit-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24px;
  .visit-info-content {
    padding: 12px 16px;
    flex: 1;
    border-radius: 8px;
    border: 1px solid #4865e8;
    font-size: 16px;
    line-height: 22px;
    word-break: break-all;
  }
  .visit-info-btn {
    border-radius: 8px;
    border: 1px solid #4865e8;
    margin-left: 24px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #4865e8;
    &:hover {
      background-color: #4865e8;
      color: white;
    }

    &.web-btn {
      width: 92px;
      height: 134px;
    }

    &.wechat-btn {
      width: 144px;
      height: 60px;
    }
  }
}

.agent-info {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #dadde8;
  // &-avatar {

  // }

  &-name {
    font-size: 18px;
    margin-top: 10px;
  }

  &-create-info {
    font-size: 14px;
    color: #969ba4;
    margin-top: 8px;
  }

  &-description {
    font-size: 14px;
    color: #646a73;
    margin-top: 16px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.use-stat {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  color: #30343a;
  border-radius: 8px;
  border: 1px solid #dadde8;
}
.chart {
  height: 275px; /* 设置图表高度 */
  width: 100%;
}
</style>
