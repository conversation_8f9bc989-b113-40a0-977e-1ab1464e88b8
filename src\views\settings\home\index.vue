<script setup lang="ts">
import ModifyCompanyLogo from './components/ModifyCompanyLogo.vue'
import ModifyCompanyName from './components/ModifyCompanyName.vue'
import RadioButton from './components/RadioButton.vue'
import {
  getHomeUserAndAgentStat,
  getHomeTokenCount,
  getHomeHotLLMModelRank,
  getHomeHotAgentRank,
  getHomeChatCount,
  getHomeTenantInfo,
  postHomeTenantAvatar,
  putHomeUpdateTenant,
} from '@/api/settings-home'
import { useStorageUsage } from '@/hooks/useStorageUsage'
import { getModelLogo } from '@/utils/models'
import { formatNumberWithCommas } from '@/utils/formatter'
defineOptions({
  name: 'SettingsHome',
})

/**
 * 公司信息 相关
 */
function useCompanyInfo() {
  type CompanyInfo = {
    tenantName: string
    avatar: string
  }
  const editLogoDialogShow = ref(false)

  const editNameDialog = ref(false)
  const companyInfo = ref<CompanyInfo>({
    tenantName: '',
    avatar: '',
  })

  const companyName = computed(() => {
    return companyInfo.value.tenantName
  })

  const companyLogo = computed(() => {
    return companyInfo.value.avatar
  })

  getTenantInfo()

  /**
   * 获取公司信息
   */
  function getTenantInfo() {
    getHomeTenantInfo<CompanyInfo>().then(res => {
      if (res.code === 200) {
        companyInfo.value = res.data
      }
    })
  }

  /**
   * 修改公司名称
   * @param tenantName 公司名称
   */
  function editCompanyInfo(tenantName: string) {
    tenantName = tenantName.trim()
    if (!tenantName) {
      ElMessage.warning('企业名称不能为空')
      return Promise.resolve(false)
    }
    return putHomeUpdateTenant({ tenantName }).then(res => {
      if (res.code === 200) {
        getTenantInfo()
        return true
      } else {
        return false
      }
    })
  }

  function createAvatarForm(data: Blob, fileName: string): FormData {
    const fd = new FormData()
    fd.append('avatarfile', data, fileName) // 固定字段名
    return fd
  }

  /**
   * 上传公司图标
   */
  function editCompanyLogo(data: Blob, fileName: string) {
    const formData = createAvatarForm(data, fileName)
    return postHomeTenantAvatar(formData).then(res => {
      if (res.code === 200) {
        getTenantInfo()
        return true
      } else {
        return false
      }
    })
  }

  return {
    companyName,
    companyLogo,
    editCompanyInfo,
    editCompanyLogo,
    editLogoDialogShow,
    editNameDialog,
    // updateCompanyInfo: getTenantInfo,
  }
}

const {
  companyName,
  companyLogo,
  editLogoDialogShow,
  editNameDialog,
  editCompanyInfo,
  editCompanyLogo,
} = useCompanyInfo()

/**
 * 企业成员和企业助手
 */
function useCompanyMemberAndAssistant() {
  type CompanyMemberAndAssistant = {
    /**
     * 待审核助手数量
     */
    agentPendingNum?: number
    /**
     * 已发布助手数量
     */
    agentPublishedNum?: number
    /**
     * 未通过助手数量
     */
    agentUnPassNum?: number
    /**
     * 正常用户的数量
     */
    userNum?: number
    /**
     * 未激活用户数量
     */
    userUnActiveNum?: number
  }
  const companyMemberAndAssistant = ref<CompanyMemberAndAssistant>({
    agentPendingNum: 0,
    agentPublishedNum: 0,
    agentUnPassNum: 0,
    userNum: 0,
    userUnActiveNum: 0,
  })

  getUserAndAgentStat()
  /**
   * 获取用户和助手统计信息
   */
  function getUserAndAgentStat() {
    getHomeUserAndAgentStat<CompanyMemberAndAssistant>().then(res => {
      if (res.code === 200) {
        companyMemberAndAssistant.value = res.data
      }
    })
  }

  return {
    companyMemberAndAssistant,
  }
}

const { companyMemberAndAssistant } = useCompanyMemberAndAssistant()

/**
 * 知识库容量
 */
const { usedSize, totalSize, usagePercentage } = useStorageUsage()

/**
 * 对话次数
 */
function useConversationCount() {
  /**
   * 1:近七天;2:昨天;3:今天
   */
  const type = ref<1 | 2 | 3>(3)
  const coversationCount = ref(0)
  function getConversationCount() {
    getHomeChatCount<number>(type.value).then(res => {
      if (res.code === 200) {
        coversationCount.value = res.data
      }
    })
  }
  watch(type, getConversationCount)
  getConversationCount()

  return {
    coversationCount,
    coversationType: type,
  }
}
const { coversationCount, coversationType } = useConversationCount()

/**
 * Token消耗统计
 */
function useTokenCount() {
  const type = ref<1 | 2 | 3>(3) // 默认今天
  const tokenCount = ref(0)

  function getTokenCount() {
    getHomeTokenCount<number>(type.value).then(res => {
      if (res.code === 200) {
        tokenCount.value = res.data
      }
    })
  }

  watch(type, getTokenCount)
  getTokenCount()

  return {
    tokenCount,
    tokenCountType: type,
  }
}

const { tokenCount, tokenCountType } = useTokenCount()

/**
 * 大语言模型排行
 */
function useLLMRankList() {
  type LLMModel = {
    /**
     * 模型Id
     */
    modelId: number
    /**
     * 模型名称
     */
    modelName: string
    /**
     * 模型消耗token数
     */
    tokenCount: number
  }
  const rankList = ref<LLMModel[]>([])

  function getRankList() {
    getHomeHotLLMModelRank<LLMModel[]>().then(res => {
      console.log('getHomeHotLLMModelRank', res)

      if (res.code === 200) {
        rankList.value = res.data
      }
    })
  }

  getRankList()

  return {
    llmRankList: rankList,
  }
}

const { llmRankList } = useLLMRankList()

/**
 * 企业助手排行
 */
function useAgentRankList() {
  type AgentModel = {
    /**
     * 助手Id
     */
    agentId?: number
    /**
     * 助手名称
     */
    agentName?: string
    /**
     * 助手话题数
     */
    topicCount?: number
    emoji: string
    backgroundColor: string
  }
  const rankList = ref<AgentModel[]>([])

  function getRankList() {
    getHomeHotAgentRank<AgentModel[]>().then(res => {
      console.log('getHomeHotAgentRank', res)

      if (res.code === 200) {
        rankList.value = res.data
      }
    })
  }

  getRankList()

  return {
    agentRankList: rankList,
  }
}

const { agentRankList } = useAgentRankList()
</script>

<template>
  <div class="settings-home-page">
    <div class="settings-home-card company-card">
      <div class="company-logo-warp" @click="editLogoDialogShow = true">
        <img class="size-full" :src="companyLogo" alt="logo" />
        <div class="edit-mask">
          <img src="@/assets/settings/home/<USER>" alt="上传" />
        </div>
      </div>
      <div class="flex-1 pl-[24px] pr-[45px] relative min-w-0">
        <h1 class="text-[20px] ell g-family-medium">{{ companyName }}</h1>
        <p class="mt-[10px]">
          <!-- 并没有会员等级 -->
          <span
            class="text-[#4865E8] text-[18px] font-bold inline-block rounded h-[32px/1] bg-[#E9E9FE] px-[17px]"
          >
            VIP
          </span>
        </p>
        <img
          class="absolute right-0 top-[50%] mt-[-20px] size-[40px] cursor-pointer"
          src="@/assets/settings/home/<USER>"
          alt=""
          @click="editNameDialog = true"
        />
      </div>
    </div>

    <div class="settings-home-card p-6 flex items-center">
      <div class="w-full">
        <el-row class="" :gutter="20">
          <el-col :span="12">
            <div class="flex items-center">
              <div class="w-[80px] mr-[24px]">
                <img
                  class="block size-[80px]"
                  src="@/assets/settings/home/<USER>"
                  alt="企业成员"
                />
                <div class="text-[18px] text-center mt-[8px] g-family-medium">企业成员</div>
              </div>
              <div class="flex-1 h-[120px] flex flex-col justify-between min-w-0">
                <div class="static-block">
                  <span class="text-[16px]">正常</span>
                  <span class="text-[#1EBF60] static-num">{{
                    formatNumberWithCommas(companyMemberAndAssistant.userNum)
                  }}</span>
                </div>
                <div class="static-block">
                  <span class="text-[16px]">未激活</span>
                  <span class="text-[#FF9349] static-num">{{
                    formatNumberWithCommas(companyMemberAndAssistant.userUnActiveNum)
                  }}</span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="flex items-center">
              <div class="w-[80px] mr-[24px]">
                <img
                  class="block size-[80px]"
                  src="@/assets/settings/home/<USER>"
                  alt="企业助手"
                />
                <div class="text-[18px] text-center mt-[8px] g-family-medium">企业助手</div>
              </div>
              <div class="flex-1 h-[120px] flex flex-col justify-between">
                <div class="static-block">
                  <span class="text-[16px]">待审核</span>

                  <span class="text-[#4865E8] static-num">{{
                    formatNumberWithCommas(companyMemberAndAssistant.agentPendingNum)
                  }}</span>
                </div>
                <div class="static-block">
                  <span class="text-[16px]">未通过</span>
                  <span class="text-[#F25B37] static-num">{{
                    formatNumberWithCommas(companyMemberAndAssistant.agentUnPassNum)
                  }}</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 助手库 -->
    <div class="settings-home-card library-card">
      <div class="w-full flex items-center">
        <div class="w-[80px] mr-[24px]">
          <span class="module-icon iconfont icon-zhushou"></span>
          <div class="text-[18px] text-center mt-[8px] g-family-medium">助手库</div>
        </div>
        <div class="flex-1 h-[140px] flex flex-col justify-between">
          <div class="flex items-center justify-between">
            <span class="text-[#4865E8] text-[16px]">已使用</span>
            <span class="text-[#969BA4] text-[16px]">max.</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-[#4865E8] text-[40px] font-600">{{
              formatNumberWithCommas(companyMemberAndAssistant.agentPublishedNum)
            }}</span>
            <span class="text-[40px] font-600">∞</span>
          </div>
          <el-progress :percentage="30" :show-text="false" :stroke-width="8" />
          <div class="text-right text-[#4865E8] text-[16px] mt-[12px]">
            <!-- <span>去扩容 &gt;</span> -->
          </div>
        </div>
      </div>
    </div>
    <!-- 企业知识库 -->
    <div class="settings-home-card library-card">
      <div class="w-full flex items-center">
        <div class="w-[89px] mr-[24px]">
          <span class="module-icon iconfont icon-zhishiku"></span>
          <div class="text-[18px] text-center mt-[8px] g-family-medium">企业知识库</div>
        </div>
        <div class="flex-1 h-[140px] flex flex-col justify-between">
          <div class="flex items-center justify-between">
            <span class="text-[#4865E8] text-[16px]">已使用</span>
            <span class="text-[#969BA4] text-[16px]">max.</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-[#4865E8] text-[40px] font-600"
              >{{ usedSize }}<span class="text-[24px] font-normal">GB</span></span
            >
            <span class="text-[40px] font-600"
              >{{ totalSize === Infinity ? '∞' : totalSize }}
              <!-- <span class="text-[24px] font-normal">GB</span> -->
            </span>
          </div>
          <el-progress :percentage="usagePercentage" :show-text="false" :stroke-width="8" />
          <div class="text-right text-[#4865E8] text-[16px] mt-[12px]">
            <!-- <span>去扩容 &gt;</span> -->
          </div>
        </div>
      </div>
    </div>
    <el-row :gutter="24">
      <el-col :span="12">
        <div class="settings-home-card pin-card">
          <RadioButton v-model="coversationType" />
          <div class="text-center">
            <div class="mt-[64px] text-[18px] g-family-medium">对话次数</div>
            <div class="text-[#4C5CEC] mt-[16px] text-[40px] mb-[10px] font-600">
              {{ formatNumberWithCommas(coversationCount) }}
            </div>
            <span class="iconfont icon-Aiduihua placeholder-icon"></span>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="settings-home-card pin-card">
          <RadioButton v-model="tokenCountType" />
          <div class="text-center">
            <div class="mt-[64px] text-[18px] g-family-medium">
              Token消耗数
              <el-tooltip
                effect="dark"
                content="统计用于对话及会话总结的大语言模型Token消耗数"
                placement="top"
              >
                <img
                  class="size-4 inline-block mt-[-15px]"
                  src="@/assets/settings/home/<USER>"
                  alt=""
                />
              </el-tooltip>
            </div>
            <!-- 千分位显示，如果数据很大很大，如何显示？ -->
            <div class="text-[#4C5CEC] mt-[16px] text-[40px] mb-[10px] font-600">
              {{ formatNumberWithCommas(tokenCount) }}
            </div>
            <img class="size-[120px] inline-block" src="@/assets/settings/home/<USER>" alt="" />
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="24">
      <el-col :span="12">
        <div class="settings-home-card rank-card">
          <div class="rank-title">
            最热大语言模型
            <el-tooltip
              effect="dark"
              content="统计用于对话及会话总结的大语言模型Token消耗数"
              placement="top"
            >
              <img
                class="size-4 inline-block mt-[-15px]"
                src="@/assets/settings/home/<USER>"
                alt=""
              />
            </el-tooltip>
          </div>
          <div class="text-[#969BA4] mt-2">仅统计近7天使用数据</div>
          <table class="w-full">
            <thead class="text-[#646A73] text-500">
              <tr>
                <th class="text-left text-4">模型名称</th>
                <th class="text-right text-4">Token数</th>
              </tr>
            </thead>
          </table>
          <div class="flex-1 overflow-auto pr-[24px] mr-[-24px]">
            <table class="w-full">
              <tbody>
                <tr v-for="llmRank in llmRankList" :key="llmRank.modelId">
                  <td class="text-left pt-2 pb-2">
                    <img
                      class="size-8 mr-2 inline-block"
                      :src="getModelLogo(llmRank.modelName)"
                      alt=""
                    />
                    <span class="text-4">{{ llmRank.modelName }}</span>
                  </td>
                  <td class="text-right text-4">
                    {{ formatNumberWithCommas(llmRank.tokenCount) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="settings-home-card rank-card">
          <div class="rank-title">最热企业助手</div>
          <div class="text-[#969BA4] mt-2">仅统计近7天使用数据</div>
          <div class="mt-0 pr-6 flex-1 overflow-auto">
            <!-- <template v-for="item in 10"> -->
            <div
              v-for="agentRank in agentRankList"
              :key="agentRank.agentId"
              class="flex items-center pt-2 pb-2"
            >
              <div
                class="emoji-wrap !size-8 !inline-flex mr-2"
                :style="{ backgroundColor: agentRank.backgroundColor }"
              >
                <img class="size-5 inline-block" :src="agentRank.emoji" alt="" />
              </div>

              <div class="flex-1 min-w-0 truncate text-4">
                {{ agentRank.agentName }}
              </div>
            </div>
            <!-- </template> -->
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
  <ModifyCompanyLogo
    v-model="editLogoDialogShow"
    :logo-src="companyLogo"
    :editCompanyLogo="editCompanyLogo"
  />
  <ModifyCompanyName
    v-model="editNameDialog"
    :editCompanyInfo="editCompanyInfo"
    :companyName="companyName"
  />
</template>

<style scoped lang="less">
.settings-home-page {
  padding: 1.5rem;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, minmax(592px, 1fr));
  grid-template-rows: minmax(200px, 250px) minmax(200px, 250px) 1fr;
  gap: 24px; /* 自适应间隙：视口2%但不小于24px */
  // row-gap: max(3vw, 24px);
}

@media (max-width: 1570px) {
  .settings-home-page {
    grid-template-columns: minmax(592px, 1fr);
  }
}
.settings-home-card {
  background: #ffffff;
  border-radius: 8px;
}

.company-card {
  padding: 1.5rem;
  display: flex;
  align-items: center;
}

.company-logo-warp {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  cursor: pointer;

  .edit-mask {
    display: flex;
    opacity: 0;
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(12, 13, 26, 0.5);
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out; /* 兼容 WebKit 内核 */
  }
  &:hover {
    .edit-mask {
      opacity: 1;
      visibility: visible;
    }
  }
}

.static-block {
  border-radius: 0.25rem;
  border-width: 1px;
  border-style: solid;
  border-color: #d5dcfb;
  height: 46px;
  padding-left: 16px;
  padding-right: 16px;
  padding-top: 10px;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 150px;
  width: 100%;

  .static-num {
    flex: 1 1 0%;
    margin-left: 0.5rem;
    font-size: 20px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    text-align: right;
  }
}

.library-card {
  padding: 35px 40px 25px;
  display: flex;
  align-items: center;
}

.module-icon {
  color: #4865e8;
  font-size: 80px !important;
  line-height: 1;
}

.placeholder-icon {
  color: rgba(72, 101, 232, 0.3);
  font-size: 120px !important;
  line-height: 1;
}

.pin-card {
  padding: 24px;
  min-height: 421px;
  height: 100%;
  max-height: 520px;
}
.rank-card {
  padding: 24px;
  min-height: 421px;
  height: 100%;
  max-height: 520px;

  display: flex;
  flex-direction: column;

  .rank-title {
    font-weight: 500;
    font-size: 18px;
    color: #30343a;
    line-height: 25px;

    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 24px;
      background: #4865e8;
      margin-right: 20px;
      vertical-align: middle;
      margin-left: -24px;
    }
  }
}
</style>
