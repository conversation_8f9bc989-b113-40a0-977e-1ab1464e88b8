import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { flushPromises, mount } from '@vue/test-utils'
import ModelInvocationDialog from '@/views/settings/usage/components/ModelInvocationDialog/index.vue'
import MemberTabContent from '@/views/settings/usage/components/ModelInvocationDialog/MemberTabContent.vue'
import ApplicationTabContent from '@/views/settings/usage/components/ModelInvocationDialog/ApplicationTabContent.vue'
import MemberInvocationDialog from '@/views/settings/usage/components/MemberInvocationDialog.vue'
import OutwardInvocationDialog from '@/views/settings/usage/components/OutwardInvocationDialog.vue'

// Mock the API functions
const {
  mockPostBizTokenUsageTimeSeries,
  mockPostBizTokenUsageAggregatePage,
  mockGetBizAiModelAvailableModels,
  // mockGetDeptTreeSelect,
  mockGetDeptUserList,
  mockQueryOutward,
  mockGetAi<PERSON>latformList,
  mockGetBizAgentAgent<PERSON>toreList,
  mockListDept,
} = vi.hoisted(() => {
  return {
    mockPostBizTokenUsageTimeSeries: vi.fn(),
    mockPostBizTokenUsageAggregatePage: vi.fn(),
    mockGetBizAiModelAvailableModels: vi.fn(),
    // mockGetDeptTreeSelect: vi.fn(),
    mockGetDeptUserList: vi.fn(),
    mockQueryOutward: vi.fn(),
    mockGetAiPlatformList: vi.fn(),
    mockGetBizAgentAgentStoreList: vi.fn(),
    mockListDept: vi.fn(),
  }
})

// Mock the usage API module
vi.mock('@/api/usage', () => {
  return {
    postBizTokenUsageTimeSeries: mockPostBizTokenUsageTimeSeries,
    postBizTokenUsageAggregatePage: mockPostBizTokenUsageAggregatePage,
    // getDeptTreeSelect: mockGetDeptTreeSelect,
    getDeptUserList: mockGetDeptUserList,
  }
})

// Mock the model API module
vi.mock('@/api', () => {
  return {
    getBizAiModelAvailableModels: mockGetBizAiModelAvailableModels
  }
})

vi.mock('@/api/external-release', () => {
  return {
    queryOutward: mockQueryOutward
  }
})

vi.mock('@/api/model-base', () => {
  return {
    getAiPlatformList: mockGetAiPlatformList
  }
})

vi.mock('@/api/agent', () => {
  return {
    getBizAgentAgentStoreList: mockGetBizAgentAgentStoreList,
  }
})

vi.mock('@/api/system/dept', () => {
  return {
    listDept: mockListDept,
  }
})

// 在文件顶部定义mock数据生成器
const generateMockData = {
  model: Array.from({ length: 25 }, (_, i) => ({
    entityId: i + 1,
    entityName: `模型${i + 1}`,
    groupName: `平台${Math.floor(i / 5) + 1}`,
    totalTokens: 1000 + i * 100,
    inputTokens: 400 + i * 50,
    outputTokens: 600 + i * 50,
    chatCount: 10 + i,
    totalCost: 0.5 + i * 0.1
  })),

  user: Array.from({ length: 30 }, (_, i) => ({
    entityId: i + 1,
    entityName: `用户${i + 1}`,
    groupName: `部门${Math.floor(i / 10) + 1}`,
    totalTokens: 800 + i * 80,
    inputTokens: 300 + i * 40,
    outputTokens: 500 + i * 40,
    chatCount: 5 + i,
    totalCost: 0.4 + i * 0.05
  })),

  outward: Array.from({ length: 15 }, (_, i) => ({
    entityId: i + 1,
    entityName: `应用${i + 1}`,
    groupName: `关联助手${Math.floor(i / 5) + 1}`,
    totalTokens: 1200 + i * 120,
    inputTokens: 500 + i * 60,
    outputTokens: 700 + i * 60,
    chatCount: 15 + i,
    totalCost: 0.6 + i * 0.15
  }))
};

function beforeTest() {
  // Clear all mocks before each test
  vi.clearAllMocks()

  // Mock the API responses
  mockPostBizTokenUsageTimeSeries.mockResolvedValue({
    code: 200,
    data: {
      dataPoints: [],
      summary: {
        totalTokens: 1000,
        inputTokens: 400,
        outputTokens: 600,
        chatCount: 10,
        totalCost: 0.5
      }
    }
  })

  mockPostBizTokenUsageAggregatePage.mockImplementation((params) => {
    const data = params.aggregateType === 'USER' ? generateMockData.user :
      params.aggregateType === 'OUTWARD' ? generateMockData.outward :
        generateMockData.model;

    const pageNum = params.pageNum || 1;
    const pageSize = params.pageSize || 10;
    const start = (pageNum - 1) * pageSize;
    return Promise.resolve({
      code: 200,
      rows: data.slice(start, start + pageSize),
      total: data.length
    });
  })

  mockGetBizAiModelAvailableModels.mockResolvedValue({
    code: 200,
    data: [
      {
        "deptId": 100,
        "parentId": 0,
        "parentName": null,
        "ancestors": "0",
        "deptName": "川建国",
        "deptCategory": null,
        "orderNum": 1,
        "leader": null,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": "1926927677828440065",
        "parentId": 100,
        "parentName": null,
        "ancestors": "0,100",
        "deptName": "土星移民局",
        "deptCategory": null,
        "orderNum": 1,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-05-26 17:05:34"
      },
      {
        "deptId": "1915220480626888706",
        "parentId": 100,
        "parentName": null,
        "ancestors": "0,100",
        "deptName": "嘉兴分公司111",
        "deptCategory": null,
        "orderNum": 2,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-04-24 09:45:20"
      },
      {
        "deptId": 101,
        "parentId": 100,
        "parentName": null,
        "ancestors": "0,100",
        "deptName": "深圳总公司",
        "deptCategory": null,
        "orderNum": 3,
        "leader": null,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": 102,
        "parentId": 100,
        "parentName": null,
        "ancestors": "0,100",
        "deptName": "长沙分公司",
        "deptCategory": null,
        "orderNum": 4,
        "leader": null,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": 104,
        "parentId": 101,
        "parentName": null,
        "ancestors": "0,100,101",
        "deptName": "市场部门1",
        "deptCategory": null,
        "orderNum": 1,
        "leader": null,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": "1915276688620621826",
        "parentId": 101,
        "parentName": null,
        "ancestors": "0,100,101",
        "deptName": "秀洲区222222",
        "deptCategory": null,
        "orderNum": 2,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-04-24 13:28:41"
      },
      {
        "deptId": 105,
        "parentId": 101,
        "parentName": null,
        "ancestors": "0,100,101",
        "deptName": "测试部门",
        "deptCategory": null,
        "orderNum": 3,
        "leader": null,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": 103,
        "parentId": 101,
        "parentName": null,
        "ancestors": "0,100,101",
        "deptName": "研发部门",
        "deptCategory": null,
        "orderNum": 4,
        "leader": 1,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": 106,
        "parentId": 101,
        "parentName": null,
        "ancestors": "0,100,101",
        "deptName": "财务部门",
        "deptCategory": null,
        "orderNum": 5,
        "leader": null,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": 107,
        "parentId": 101,
        "parentName": null,
        "ancestors": "0,100,101",
        "deptName": "运维部门",
        "deptCategory": null,
        "orderNum": 6,
        "leader": null,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": "1915294937009721346",
        "parentId": "1915276688620621826",
        "parentName": null,
        "ancestors": "0,100,101,1915276688620621826",
        "deptName": "33",
        "deptCategory": null,
        "orderNum": 1,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-04-24 14:41:12"
      },
      {
        "deptId": 108,
        "parentId": 102,
        "parentName": null,
        "ancestors": "0,100,102",
        "deptName": "市场部门",
        "deptCategory": null,
        "orderNum": 1,
        "leader": null,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": 109,
        "parentId": 102,
        "parentName": null,
        "ancestors": "0,100,102",
        "deptName": "财务部门",
        "deptCategory": null,
        "orderNum": 2,
        "leader": null,
        "leaderName": null,
        "phone": "15888888888",
        "email": "<EMAIL>",
        "status": "0",
        "createTime": "2024-09-09 09:53:28"
      },
      {
        "deptId": "1933443984841211906",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "test1",
        "deptCategory": null,
        "orderNum": 1,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-13 16:39:02"
      },
      {
        "deptId": "1933444123823669249",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "test111",
        "deptCategory": null,
        "orderNum": 2,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-13 16:39:35"
      },
      {
        "deptId": "1938420221036797954",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "test14",
        "deptCategory": null,
        "orderNum": 3,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-27 10:12:49"
      },
      {
        "deptId": "1933444151300554753",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "test1111",
        "deptCategory": null,
        "orderNum": 4,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-13 16:39:42"
      },
      {
        "deptId": "1933444094782308354",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "test11",
        "deptCategory": null,
        "orderNum": 5,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-13 16:39:29"
      },
      {
        "deptId": "1934447673244721154",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "test133",
        "deptCategory": null,
        "orderNum": 6,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-16 11:07:20"
      },
      {
        "deptId": "1933444174159511554",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "test11111",
        "deptCategory": null,
        "orderNum": 7,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-13 16:39:47"
      },
      {
        "deptId": "1933444198998179842",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "test12",
        "deptCategory": null,
        "orderNum": 8,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-13 16:39:53"
      },
      {
        "deptId": "1933444222758912001",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "test122",
        "deptCategory": null,
        "orderNum": 9,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-13 16:39:59"
      },
      {
        "deptId": "1934447598841962497",
        "parentId": "1915220480626888706",
        "parentName": null,
        "ancestors": "0,100,1915220480626888706",
        "deptName": "tes13",
        "deptCategory": null,
        "orderNum": 10,
        "leader": null,
        "leaderName": null,
        "phone": null,
        "email": null,
        "status": "0",
        "createTime": "2025-06-16 11:07:03"
      }
    ]
  })

  mockListDept.mockResolvedValue({
    code: 200,
    data: [
      {
        deptId: 1,
        deptName: '部门1'
      }
    ]
  })

  mockGetDeptUserList.mockResolvedValue({
    code: 200,
    data: [
      {
        userId: 1,
        nickName: '用户1'
      }
    ]
  })

  mockQueryOutward.mockResolvedValue({
    code: 200,
    rows: [
      {
        id: 1,
        name: '应用1',
        outwardType: { code: 'API' },
        enabled: true,
        tokens: 1000,
        maxTokens: 5000
      }
    ],
    total: 1
  })

  mockGetAiPlatformList.mockResolvedValue({
    code: 200,
    rows: [
      {
        id: '1',
        platformName: '平台1',
        platformCode: 'platform1',
        isEnabled: true,
        description: '平台1描述',
        icon: 'icon-platform1',
        baseUrl: 'https://api.platform1.com',
        apiKey: 'api-key-1'
      }
    ],
    total: 1
  })

  mockGetBizAgentAgentStoreList.mockResolvedValue({
    code: 200,
    rows: [
      {
        backgroundColor: "#f0f0f0",
        categoryId: "category123",
        categoryIdList: ["category123", "category456"],
        description: "这是一个示例助手，用于演示 AgentVo 接口的数据结构。",
        emoji: "🤖",
        id: "agent789",
        isPrivate: false,
        modelId: "model123",
        name: "示例助手",
        prompt: "你是一个有用的助手。",
        status: 1
      }
    ]
  })

}

function afterTest() {
  // Clean up after each test
  vi.restoreAllMocks()
}


describe('查看模型统计的详情-ModelInvocationDialog', () => {
  beforeEach(beforeTest)

  afterEach(afterTest)

  const startDate = '2025-08-05'
  const endDate = '2025-08-20'
  const mockData = {
    "entityId": "1947182914501808129",
    "entityName": "THUDM/GLM-4.1V-9B-Thinking",
    "groupName": "硅基流动",
    "type": "text",
    "platformIconUrl": "",
    "emoji": "",
    "backgroundColor": "",
    "totalTokens": 62985,
    "inputTokens": 23730,
    "outputTokens": 39255,
    "chatCount": 177,
    "totalCost": 0
  }

  it('数据展示正确', async () => {
    const wrapper = mount(ModelInvocationDialog, {
      global: {
        stubs: {
          transition: false
        }
      }
    })

    wrapper.vm.editData(
      mockData,
      startDate,
      endDate
    )
    await flushPromises()
    expect(wrapper.text()).toContain(startDate)
    expect(wrapper.text()).toContain(endDate)
    expect(wrapper.text()).toContain('THUDM/GLM-4.1V-9B-Thinking')
    expect(wrapper.text()).toContain('硅基流动')
    expect(wrapper.text()).toContain('62,985')
    expect(wrapper.text()).toContain('23,730')
    expect(wrapper.text()).toContain('39,255')
    expect(wrapper.text()).toContain('成员调用')
    expect(wrapper.text()).toContain('应用调用')
    const activeTab = wrapper.find('.model-invocation-tabs__tab--active')
    expect(activeTab.text()).toContain('成员调用')

    // 姓名输入框
    const nameInput = wrapper.find('[placeholder="姓名"]')
    expect(nameInput.exists()).toBe(true)

    // 选择部门选择框
    expect(wrapper.text()).toContain('选择部门')
  })

  it('成员调用 和 应用调用 切换', async () => {
    vi.useRealTimers()
    const wrapper = mount(ModelInvocationDialog, {
      attachTo: document.body,
      global: {
        stubs: {
          transition: false,
          'transition-group': false
        }
      }
    })
    wrapper.vm.editData(
      mockData,
      startDate,
      endDate
    )
    await flushPromises()

    expect(mockPostBizTokenUsageAggregatePage).toBeCalledWith({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      aggregateType: 'USER',
      pageNum: 1,
      pageSize: 10,
      nickNames: [],
      modelIds: ['1947182914501808129']
    })
    expect(wrapper.findComponent(MemberTabContent).isVisible()).toBe(true)
    expect(wrapper.findComponent(ApplicationTabContent).isVisible()).toBe(false)
    await wrapper.findAll('.model-invocation-tabs__tab')[1].trigger('click')
    await flushPromises()
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 100))
    expect(mockPostBizTokenUsageAggregatePage).toBeCalledWith({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      aggregateType: 'OUTWARD',
      pageNum: 1,
      pageSize: 10,
      agentIds: [],
      outwardNames: [],
      outwardTypes: [],
      modelIds: ['1947182914501808129']
    })
    expect(wrapper.findComponent(MemberTabContent).isVisible()).toBe(false)
    expect(wrapper.findComponent(ApplicationTabContent).isVisible()).toBe(true)
  })

  it('成员调用 和 应用调用，搜索查询', async () => {
    vi.useFakeTimers()
    const wrapper = mount(ModelInvocationDialog, {
      attachTo: document.body,
      global: {
        stubs: {
          transition: false,
          'transition-group': false
        }
      }
    })

    wrapper.vm.editData(
      mockData,
      startDate,
      endDate
    )
    await flushPromises()

    // 测试成员调用tab的搜索功能
    const nameInput = wrapper.find('[placeholder="姓名"]')
    await nameInput.setValue('测试用户')
    await wrapper.find('.search-button').trigger('click')
    await flushPromises()

    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenLastCalledWith({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      aggregateType: 'USER',
      pageNum: 1,
      pageSize: 10,
      nickNames: ['测试用户'],
      modelIds: ['1947182914501808129']
    })

    // 测试应用调用tab的搜索功能
    await wrapper.findAll('.model-invocation-tabs__tab')[1].trigger('click')
    await flushPromises()
    const appTab = wrapper.findComponent(ApplicationTabContent)

    const appNameInput = appTab.find('[placeholder="应用名称"]')
    await appNameInput.setValue('测试应用')
    await appTab.find('.search-button').trigger('click')
    await flushPromises()

    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenLastCalledWith({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      aggregateType: 'OUTWARD',
      pageNum: 1,
      pageSize: 10,
      agentIds: [],
      outwardNames: ['测试应用'],
      outwardTypes: [],
      modelIds: ['1947182914501808129']
    })
  })

  it('成员调用 和 应用调用，翻页', async () => {
    const wrapper = mount(ModelInvocationDialog, {
      attachTo: document.body,
      global: {
        stubs: {
          transition: false,
          'transition-group': false
        }
      }
    })

    wrapper.vm.editData(
      mockData,
      startDate,
      endDate
    )
    await flushPromises()

    // 测试成员调用tab的分页功能
    const pagination = wrapper.findComponent({ name: 'ElPagination' })
    await pagination.vm.$emit('current-change', 2)
    await flushPromises()

    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenLastCalledWith({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      aggregateType: 'USER',
      pageNum: 2,
      pageSize: 10,
      nickNames: [],
      modelIds: ['1947182914501808129']
    })

    // 测试应用调用tab的分页功能
    await wrapper.findAll('.model-invocation-tabs__tab')[1].trigger('click')
    await flushPromises()
    const appTab = wrapper.findComponent(ApplicationTabContent)
    await appTab.findComponent({ name: 'ElPagination' }).vm.$emit('current-change', 3)
    await flushPromises()

    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenLastCalledWith({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      aggregateType: 'OUTWARD',
      pageNum: 3,
      pageSize: 10,
      agentIds: [],
      outwardNames: [],
      outwardTypes: [],
      modelIds: ['1947182914501808129']
    })
  })

  it('成员调用 和 应用调用，表格展示', async () => {
    const wrapper = mount(ModelInvocationDialog, {
      attachTo: document.body,
      global: {
        stubs: {
          transition: false,
          'transition-group': false
        }
      }
    })
    wrapper.vm.editData(
      mockData,
      startDate,
      endDate
    )
    await flushPromises()

    // 测试成员调用tab的表格展示
    const memberTab = wrapper.findComponent(MemberTabContent)
    expect(memberTab.text()).toContain('姓名')
    expect(memberTab.text()).toContain('部门')
    expect(memberTab.text()).toContain('输入Tokens数')
    expect(memberTab.text()).toContain('输出Tokens数')
    expect(memberTab.text()).toContain('调用总量Tokens数')

    // 测试应用调用tab的表格展示
    await wrapper.findAll('.model-invocation-tabs__tab')[1].trigger('click')
    await flushPromises()

    const appTab = wrapper.findComponent(ApplicationTabContent)
    expect(appTab.text()).toContain('应用名称')
    expect(appTab.text()).toContain('关联助手')
    expect(appTab.text()).toContain('输入Tokens数')
    expect(appTab.text()).toContain('输出Tokens数')
    expect(appTab.text()).toContain('调用总量Tokens数')
  })
});

describe('查看成员统计的详情-MemberInvocationDialog', () => {
  beforeEach(beforeTest)

  afterEach(afterTest)
  const startDate = '2025-08-05'
  const endDate = '2025-08-20'
  // 测试数据
  const mockData = {
    "entityId": 1,
    "entityName": "狮佬",
    "groupName": "研发部门",
    "type": null,
    "platformIconUrl": null,
    "emoji": null,
    "backgroundColor": null,
    "totalTokens": 42320,
    "inputTokens": 8660,
    "outputTokens": 33660,
    "chatCount": 79,
    "totalCost": 0
  }

  it('弹出框标题是否正确', async () => {
    const wrapper = mount(MemberInvocationDialog)

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )
    expect(wrapper.text()).toContain('研发部门')
    expect(wrapper.text()).toContain('狮佬')
    expect(wrapper.text()).toContain(startDate)
    expect(wrapper.text()).toContain(endDate)
  })

  it('研发部门、人员、日期显示正确', async () => {
    const wrapper = mount(MemberInvocationDialog)

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )

    expect(wrapper.text()).toContain('模型调用量明细')
  })

  it('tokens使用数据展示格式正确', async () => {
    const wrapper = mount(MemberInvocationDialog)

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )
    expect(wrapper.text()).toContain('42,320')
    expect(wrapper.text()).toContain('8,660')
    expect(wrapper.text()).toContain('33,660')
  })

  it('表格列名和顺序正确', async () => {
    const wrapper = mount(MemberInvocationDialog, {
      global: {
        stubs: {
          transition: false,
          'transition-group': false
        }
      }
    })

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )

    await flushPromises()

    const elTable = wrapper.findComponent({ name: 'ElTable' })
    expect(elTable.text()).toContain('序号')
    expect(elTable.text()).toContain('模型')
    expect(elTable.text()).toContain('平台')
    expect(elTable.text()).toContain('输入Tokens数')
    expect(elTable.text()).toContain('输出Tokens数')
    expect(elTable.text()).toContain('调用总量Tokens数')
  })

  it('搜索功能正常', async () => {
    const wrapper = mount(MemberInvocationDialog, {
      global: {
        stubs: {
          transition: false,
          'transition-group': false
        }
      }
    })

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )

    // 模拟姓名搜索
    const nameInput = wrapper.findComponent({ name: 'ElInput' })
    await nameInput.setValue('deep')
    const elSelect = wrapper.findComponent({ name: 'ElSelect' })
    await elSelect.setValue(['1'])
    await wrapper.find('.search-button').trigger('click')
    await flushPromises()
    expect(mockPostBizTokenUsageAggregatePage).toBeCalledWith({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      aggregateType: 'MODEL',
      pageNum: 1,
      pageSize: 10,
      modelNames: ['deep'],
      userIds: [1],
      platformIds: ['1']
    })
  })

  it('翻页功能正常', async () => {
    const wrapper = mount(MemberInvocationDialog, {
      global: {
        stubs: {
          transition: false,
          'transition-group': false
        }
      }
    })

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )

    // 模拟翻页
    const pagination = wrapper.findComponent({ name: 'ElPagination' })
    await pagination.vm.$emit('current-change', 2)
    await flushPromises()
    expect(mockPostBizTokenUsageAggregatePage).toBeCalledWith({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      aggregateType: 'MODEL',
      pageNum: 2,
      pageSize: 10,
      modelNames: [],
      userIds: [1],
      platformIds: []
    })
  })
})

describe('查看应用统计的详情-OutwardInvocationDialog', () => {
  beforeEach(beforeTest)

  afterEach(afterTest)

  const startDate = '2025-08-05'
  const endDate = '2025-08-20'
  // 测试数据
  const mockData = {
    "entityId": "1952538272115822593",
    "entityName": "测试飞书机器人",
    "groupName": "图文对话助手",
    "type": "FEISHU",
    "platformIconUrl": null,
    "emoji": "/static/emoji/1f600.png",
    "backgroundColor": "#FFEBD4",
    "totalTokens": 15302,
    "inputTokens": 5860,
    "outputTokens": 9442,
    "chatCount": 22,
    "totalCost": "0.0000"
  }

  it('弹出框标题是否正确', async () => {
    const wrapper = mount(OutwardInvocationDialog)

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )
    expect(wrapper.text()).toContain('应用调用量明细')
  })

  it('应用名称和时间范围显示正确', async () => {
    const wrapper = mount(OutwardInvocationDialog)

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )
    expect(wrapper.text()).toContain('测试飞书机器人')
    expect(wrapper.text()).toContain(startDate)
    expect(wrapper.text()).toContain(endDate)
  })

  it('tokens使用数据展示格式正确', async () => {
    const wrapper = mount(OutwardInvocationDialog)

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )
    expect(wrapper.text()).toContain('15,302')
    expect(wrapper.text()).toContain('5,860')
    expect(wrapper.text()).toContain('9,442')
  })

  it('表格列名和顺序正确', async () => {
    const wrapper = mount(OutwardInvocationDialog, {
      global: {
        stubs: {
          transition: false,
          'transition-group': false
        }
      }
    })

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )

    await flushPromises()

    const elTable = wrapper.findComponent({ name: 'ElTable' })
    expect(elTable.text()).toContain('序号')
    expect(elTable.text()).toContain('模型')
    expect(elTable.text()).toContain('平台')
    expect(elTable.text()).toContain('输入Tokens数')
    expect(elTable.text()).toContain('输出Tokens数')
    expect(elTable.text()).toContain('调用总量Tokens数')
  })

  it('翻页功能正常', async () => {
    const wrapper = mount(OutwardInvocationDialog, {
      global: {
        stubs: {
          transition: false,
          'transition-group': false
        }
      }
    })

    await wrapper.vm.editData(
      mockData, startDate, endDate
    )

    // 模拟翻页
    const pagination = wrapper.findComponent({ name: 'ElPagination' })
    await pagination.vm.$emit('current-change', 2)
    await flushPromises()
    expect(mockPostBizTokenUsageAggregatePage).toBeCalledWith({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59',
      aggregateType: 'MODEL',
      pageNum: 2,
      pageSize: 10,
      outwardIds: ["1952538272115822593"]
    })
  })
})