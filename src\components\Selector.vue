<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue' // 添加 ref 和 computed
import { ElInput, ElIcon } from 'element-plus' // 导入 ElInput 和 ElIcon
import { CaretBottom, CaretTop, Search, CircleClose } from '@element-plus/icons-vue' // 导入图标
import { vAutoAnimate } from '@formkit/auto-animate/vue'
import { getModelLogo } from '@/utils/models'
import { filterListBySearchQuery, handleGroupList } from '@/utils/search'
import { useFormItem } from 'element-plus'
defineOptions({
  name: 'Selector',
})

const props = withDefaults(
  defineProps<{
    opentionList?: { label: string; value: string | number; [key: string]: any }[]
    loading?: boolean
    width?: number
    disabled?: boolean
    clearable?: boolean
    placeholder?: string
    /**
     * 分组字段
     */
    groupBy?: string
    searchPlaceholder?: string
  }>(),
  {
    opentionList: () => [],
    loading: false,
    disabled: false,
    clearable: false,
    placeholder: '',
    groupBy: 'platformName',
    searchPlaceholder: '搜索',
  },
)

const emits = defineEmits(['change', 'update', 'update:selectedModel'])
const attrs = useAttrs()
const currentModel = defineModel<string | number>({ default: '' })

// 添加 ref 来引用 .trigger 元素
const triggerRef = ref<HTMLElement | null>(null)
// 添加响应式变量来存储宽度
// const triggerWidth = ref<number>(0)

const { formItem } = useFormItem()

watch(currentModel, newVal => {
  emits('change', newVal)

  if (formItem?.validate) {
    formItem?.validate('change')
  }
})

const isOpen = ref(false) // 新增展开状态
const searchQuery = ref('') // 新增搜索状态

const isFocus = ref(false)

const selectModel = (model: any) => {
  currentModel.value = model.value
  isOpen.value = false
}

const selectedModel = computed(() => {
  return props.opentionList.find(model => model.value === currentModel.value)
})

// 过滤模型列表
const filteredModels = computed(() => {
  const filterList = props.opentionList || []
  if (!searchQuery.value) return handleGroupList(filterList, props.groupBy)
  const query = searchQuery.value.toLowerCase()
  const list = filterListBySearchQuery(filterList, query, 'label')
  return handleGroupList(list, props.groupBy)
})

const isHovering = ref(false)

const handleMouseEnter = () => {
  if (props.disabled || !props.clearable) return // 如果禁用则不处理鼠标悬停事件{
  isHovering.value = true
}

const handleMouseLeave = () => {
  if (props.disabled) return // 如果禁用则不处理鼠标悬停事件{
  isHovering.value = false
}

const clearSelection = (event: MouseEvent) => {
  event.stopPropagation()
  currentModel.value = ''
}

// ESC键关闭下拉菜单
const handleEsc = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    isOpen.value = false
  }
}

// 监听 isOpen 的变化，在 document 上添加和移除 keydown 事件监听器
watch(isOpen, newVal => {
  if (newVal) {
    // 下拉菜单打开时，添加事件监听器
    document.addEventListener('keydown', handleEsc)
  } else {
    // 下拉菜单关闭时，移除事件监听器
    document.removeEventListener('keydown', handleEsc)
  }
})

// 组件卸载前移除事件监听器
onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleEsc)
})

function useElementWidth(elementRef: Ref<HTMLElement | null>) {
  const width = ref(0)
  const resizeObserver = ref<ResizeObserver | null>(null)

  const updateWidth = () => {
    if (elementRef.value) {
      const rect = elementRef.value.getBoundingClientRect()
      width.value = rect.width
    }
  }

  const getResizeObserver = () => {
    if (typeof window !== 'undefined') {
      return window.ResizeObserver || (window as any).ResizeObserverPolyfill
    }
    return null
  }

  const handleResize = (entries: ResizeObserverEntry[]) => {
    // console.log(entries, 'entries')

    for (let entry of entries) {
      const newWidth = entry.borderBoxSize[0].inlineSize
      if (Math.abs(newWidth - width.value) > 1) {
        width.value = newWidth
      }
    }
  }

  onMounted(() => {
    updateWidth()

    const ResizeObserverClass = getResizeObserver()
    if (ResizeObserverClass && elementRef.value) {
      resizeObserver.value = new ResizeObserverClass(handleResize)
      resizeObserver.value.observe(elementRef.value)
    }
  })

  onUnmounted(() => {
    if (resizeObserver.value && elementRef.value) {
      resizeObserver.value.unobserve(elementRef.value)
      resizeObserver.value.disconnect()
      resizeObserver.value = null
    }
  })

  return { width, updateWidth }
}

const { width: triggerWidth } = useElementWidth(triggerRef)
</script>

<template>
  <el-popover
    :width="triggerWidth"
    popper-class="model-popover"
    placement="bottom"
    trigger="click"
    :disabled="disabled"
    v-model:visible="isOpen"
    @hide="searchQuery = ''"
  >
    <template #reference>
      <div
        ref="triggerRef"
        class="trigger"
        v-bind="attrs"
        tabindex="0"
        role="select"
        :style="{ width: width ? width + 'px' : '' }"
        :class="{ 'is-disabled': disabled }"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <slot name="logo" :option="selectedModel">
          <img
            v-if="selectedModel && getModelLogo(selectedModel?.name, '')"
            :src="getModelLogo(selectedModel?.name, '')"
            class="model-logo"
          />
        </slot>

        <span class="model-name">{{ selectedModel?.label || placeholder }}</span>
        <el-icon v-show="!isOpen && (!selectedModel || !isHovering)"><CaretBottom /></el-icon>
        <el-icon v-show="isOpen && (!selectedModel || !isHovering)"><CaretTop /></el-icon>
        <el-icon
          v-if="clearable"
          v-show="selectedModel && isHovering"
          @click.stop="clearSelection"
          role="button"
          aria-label="clear"
          ><CircleClose
        /></el-icon>
      </div>
    </template>
    <div class="dropdown-menu" v-loading="loading" aria-label="model-dropdown">
      <!-- 搜索框 -->
      <div class="search-box" :class="{ 'is-focus': isFocus }">
        <el-input
          class="model-search"
          v-model="searchQuery"
          :placeholder="searchPlaceholder"
          clearable
          @click.stop
          @focus="isFocus = true"
          @blur="isFocus = false"
          aria-label="search-box"
        />
        <el-icon class="search-icon" size="18"><Search /></el-icon>
      </div>
      <div class="model-list" v-auto-animate>
        <div class="mb-6" v-for="item in filteredModels">
          <div
            v-if="item.groupName"
            aria-label="group"
            class="text-[#646A73] text-[16px] mb-[12px]"
          >
            {{ item.groupName }}
          </div>
          <div
            v-for="model in item.list"
            :key="model.value"
            class="menu-item"
            :class="{ active: model.value === currentModel }"
            role="option"
            @click="selectModel(model)"
          >
            <div class="truncate">
              <slot name="logo" :option="model">
                <img
                  v-if="getModelLogo(model?.label, '')"
                  :src="getModelLogo(model?.label, '')"
                  class="model-logo"
                />
              </slot>

              {{ model.label }}
            </div>
          </div>
        </div>
        <el-empty
          v-if="filteredModels.length === 0"
          :description="!searchQuery && opentionList.length === 0 ? '暂无数据' : '未查询到相关数据'"
        />
      </div>
    </div>
  </el-popover>
</template>

<style lang="less" scoped>
.trigger {
  // width: auto;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  height: 32px; /* 确保高度与 NSelect 默认高度一致 */
  box-sizing: border-box; /* 包含 padding 和 border 在宽度内 */
  background: #fff;

  &:hover {
    border-color: #ecf1ff;
    background: #f0f7ff;
    transition: all 0.2s;
  }

  .model-logo {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    flex-shrink: 0; /* 防止图片缩小 */
  }

  .model-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap; /* 防止文本换行 */
  }

  .el-icon {
    transition: transform 0.3s;
    margin-left: 8px;
    font-size: 16px; /* 调整图标大小 */
    flex-shrink: 0; /* 防止图标缩小 */
  }

  &:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    outline: none;
  }
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s;
  }
}

.is-disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: #f5f5f5 !important;
  &:hover {
    border-color: #dcdfe6 !important;
    background: #f5f5f5 !important;
  }
  &:focus {
    border-color: #dcdfe6 !important;
    box-shadow: none;
    outline: none;
  }
  &:active {
    transform: scale(1);
  }
}

.dropdown-menu {
  .search-box {
    position: relative;
    padding-right: 24px;
    margin-bottom: 24px;
    border-bottom: 1px solid #e8e8e8;
    &.is-focus {
      border-color: #409eff;
    }

    &:hover {
      border-color: #409eff;
    }

    .model-search:deep(.el-input__wrapper) {
      box-shadow: none;

      &.is-focus {
        box-shadow: none;
      }
    }

    .search-icon {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .model-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .fixed-section {
    // border-bottom: 1px solid #f0f0f0;
    padding-bottom: 24px; /* 增加底部内边距 */
    .fixed-item {
      &:hover {
        background: #e6eaf4;
      }
    }
  }

  .divider {
    height: 1px;
    background: #f0f0f0;
    margin: 8px 0;
  }

  .menu-item {
    padding: 8px;
    margin-right: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.active {
      background-color: #ecf5ff;
      border: 1px solid #d9ecff;
      color: #409eff;
      border-radius: 8px;
    }

    .fixed-icon {
      display: inline-block;
      width: 22px;
      visibility: hidden;
    }

    &:hover {
      background: #e6eaf4;
      border-radius: 8px;

      .fixed-icon {
        visibility: visible;
      }
    }

    .model-logo {
      display: inline-block;
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
}
</style>

<style>
.model-popover.el-popover.el-popper {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0px 2px 8px 2px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  backdrop-filter: blur(8px);
}
</style>
