export const formatFileSize = (size: number | undefined | null): string => {
  if (size === null || size === undefined) {
    return '0 KB'
  }
  const units = ['KB', 'MB', 'GB', 'TB', 'PB']
  let unitIndex = 0
  let calculatedSize = size / 1024

  while (calculatedSize > 1024 && unitIndex < units.length - 1) {
    calculatedSize /= 1024
    unitIndex++
  }

  return `${calculatedSize.toFixed(2)} ${units[unitIndex]}`
};

/**
 * 数字千分位格式化
 * @param num 需要格式化的数字
 * @returns 千分位格式的字符串
 */
export const formatNumberWithCommas = (num: any): string => {
  num = Number(num)

  // 检查是否为有效数字
  if (isNaN(num)) {
    return '-'; // 或根据需求返回其他值
  }
  return new Intl.NumberFormat('en-US').format(num);
};


/**
 * token 展示
 * @param num 需要格式化的token数量
 * @returns 格式化后的字符串
 *
 * 小于100000：返回千分位格式（如1,234）
 * 大于等于100000：除以1000，保留两位小数，返回千分位格式拼接K（如1.23K）
 */
export function formatToken(num: number): string {
  if (isNaN(num)) return '';
  if (!isFinite(num)) return num > 0 ? '∞' : '-∞';

  if (num >= 0) {
    return num < 100000
      ? formatNumberWithCommas(Math.floor(num))
      : `${formatNumberWithCommas(+(num / 1000).toFixed(2))}K`;
  } else {
    return num > -100000 ? formatNumberWithCommas(Math.floor(num)) : `${formatNumberWithCommas(+(num / 1000).toFixed(2))}K`;
  }
}