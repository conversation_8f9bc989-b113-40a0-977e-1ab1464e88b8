import type { Ref } from 'vue'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getBizAiModelAvailableModels,
  getBizChatContentRecordList,
  getBizGuestChatContentRecordList,
} from '@/api'
import { useChat } from '@/views/chat/hooks/useChat'
import { useChatStore } from '@/store'
import { getCurrentToken } from '@/utils/token-manager'
import { t } from '@/locales'
import ms from '@/utils/message'
import { usePublicChat } from '@/views/chat/hooks/usePublicChat'

// 会话流式响应状态接口
interface ConversationStreamState {
  loading: Ref<boolean>
  controller: AbortController
  currentReader: ReadableStreamDefaultReader<Uint8Array> | null
  conversationId: string
  // 添加超时定时器引用
  timeoutTimer?: NodeJS.Timeout
  // 添加累积内容，用于会话恢复时的状态同步
  accumulatedContent?: string
  // 添加累积推理内容，用于会话恢复时的状态同步
  accumulatedReasoningContent?: string
  currentIndex?: number
}

// 流式响应管理器类
class StreamResponseManager {
  // 存储所有会话的流式响应状态
  private conversationStates = new Map<string, ConversationStreamState>()

  // 统一的超时时长常量
  private readonly TIMEOUT_MS = 29000

  // 获取或创建会话状态
  getOrCreateState(conversationId: string): ConversationStreamState {
    if (!this.conversationStates.has(conversationId)) {
      this.conversationStates.set(conversationId, {
        loading: ref(false),
        controller: new AbortController(),
        currentReader: null,
        conversationId,
      })
    }
    return this.conversationStates.get(conversationId)!
  }

  // 获取会话状态（不创建）
  getState(conversationId: string): ConversationStreamState | undefined {
    return this.conversationStates.get(conversationId)
  }

  // 检查会话是否正在加载
  isLoading(conversationId: string): boolean {
    const state = this.getState(conversationId)
    // console.log(`[isLoading] 会话 ${conversationId} 当前loading状态:`, state?.loading.value)
    return state?.loading.value ?? false
  }

  // 设置会话加载状态
  setLoading(conversationId: string, loading: boolean): void {
    const state = this.getOrCreateState(conversationId)
    const oldValue = state.loading.value
    state.loading.value = loading

    // 调试日志：跟踪loading状态变化
    if (oldValue !== loading) {
      console.log(
        `[StreamManager] 会话 ${conversationId} loading状态变化: ${oldValue} -> ${loading}`,
      )
      console.trace() // 输出调用栈，帮助定位是谁修改了状态
    }
  }

  // 统一的资源清理方法 - 集中管理所有清理逻辑
  private cleanupConversationResources(state: ConversationStreamState): void {
    this.cleanupConversationResourcesWithoutLoading(state)
    // 更新loading状态
    const oldValue = state.loading.value
    state.loading.value = false

    // 调试日志
    if (oldValue) {
      console.log(
        `[StreamManager] cleanupConversationResources 将loading设置为false，会话ID: ${state.conversationId}`,
      )
      console.trace()
    }
  }

  // 清理资源但不设置loading状态（用于避免竞态条件）
  private cleanupConversationResourcesWithoutLoading(state: ConversationStreamState): void {
    // 清理超时定时器
    if (state.timeoutTimer) {
      clearInterval(state.timeoutTimer)
      state.timeoutTimer = undefined
    }

    // 清理Reader
    if (state.currentReader) {
      try {
        state.currentReader.cancel('清理资源')
      } catch (err) {
        console.error('取消Reader时出错:', err)
      }
      state.currentReader = null
    }

    // 清理恢复数据
    state.accumulatedContent = undefined
    state.accumulatedReasoningContent = undefined
    state.currentIndex = undefined
  }

  // 停止会话的流式响应
  async stopConversation(conversationId: string): Promise<boolean> {
    const state = this.getState(conversationId)
    if (!state || !state.loading.value) {
      return false
    }

    console.log(`停止会话 ${conversationId} 的流式响应`)

    // 先标记为停止状态，避免与新请求冲突
    console.log(`[StreamManager] stopConversation 将loading设置为false，会话ID: ${conversationId}`)
    state.loading.value = false

    // 然后清理其他资源，但不再重复设置loading状态
    this.cleanupConversationResourcesWithoutLoading(state)
    return true
  }

  // 创建新的控制器
  resetController(conversationId: string): AbortController {
    const state = this.getOrCreateState(conversationId)
    state.controller = new AbortController()
    return state.controller
  }

  // 集中的错误处理方法
  private processStreamError(
    conversationId: string,
    index: number,
    errorMessage: string,
    accumulatedContent = '',
  ): void {
    const state = this.getState(conversationId)
    if (state) {
      this.cleanupConversationResources(state)
    }

    const { updateChat } = useChat()
    const chatStore = useChatStore()
    const finalText = accumulatedContent
      ? `${accumulatedContent}\n[${errorMessage}]`
      : `[${errorMessage}]`

    // 保存原有的conversationOptions，避免丢失消息id
    const originalConversationOptions =
      chatStore.getChatByConversationId(conversationId)[index]?.conversationOptions

    updateChat(conversationId, index, {
      dateTime: new Date().toLocaleString(),
      text: finalText,
      inversion: false,
      error: true,
      loading: false,
      conversationOptions: originalConversationOptions, // 保持原有的conversationOptions，确保id不丢失
    })

    ms.error(errorMessage)
  }

  // 集中的超时管理方法
  private setupTimeout(
    state: ConversationStreamState,
    conversationId: string,
    index: number,
  ): void {
    let lastMessageTime = Date.now()
    let hasStartEvent = false
    let startEventTime = 0

    state.timeoutTimer = setInterval(() => {
      const now = Date.now()
      const accumulatedContent = state.accumulatedContent || ''
      const accumulatedReasoningContent = state.accumulatedReasoningContent || ''

      // 如果收到START事件但超过29秒没有后续数据，判定为异常
      // resolved 判断条件应该加上accumulatedReasoningContent
      if (
        hasStartEvent &&
        !accumulatedContent &&
        !accumulatedReasoningContent &&
        now - startEventTime > this.TIMEOUT_MS
      ) {
        this.processStreamError(conversationId, index, '服务器响应异常，收到START事件后无内容返回')
        return
      }

      // 常规超时检测
      if (now - lastMessageTime > this.TIMEOUT_MS) {
        this.processStreamError(conversationId, index, '服务器响应超时，请重试', accumulatedContent)
      }
    }, 1000)

    // 暴露更新时间的方法给外部
    ;(state as any).updateLastMessageTime = () => {
      lastMessageTime = Date.now()
    }
    ;(state as any).setStartEvent = () => {
      hasStartEvent = true
      startEventTime = Date.now()
    }
  }

  // 处理流式响应的核心方法
  // 接口：biz/conversation/completions，需要支持两种格式，用来适配deepseekR1和openAI不同的推理格式
  // 1、通过reasoningContent字段输出推理
  // 2、通过content字段中包含<think>...</think>输出
  // resolved coreStreamResponse流式输出需要同时适配deepseekR1和openAI不同的推理格式，目前的逻辑符合openAI推理格式，deepseekR1的推理格式参考src\.refer\Response.txt
  // resolved 通过reasoningContent字段输出推理的情况，目前ai回复内容前端就能渲染出"已深度思考"，但是通过content字段中包含<think>...</think>输出的情况，前端无法在接收到</think>前渲染出"已深度思考"
  async coreStreamResponse(
    conversationId: string,
    response: Response,
    index: number,
    userMessage: string,
    getSummary: (conversationId: string) => Promise<void>,
    latestChatId: Ref<string | undefined>,
    scrollToBottomIfAtBottom: () => void,
    scrollToBottom?: () => void,
  ): Promise<void> {
    const state = this.getOrCreateState(conversationId)
    const { updateChat, updateChatSome } = useChat()
    const chatStore = useChatStore()

    const reader = response.body?.getReader()
    const decoder = new TextDecoder()
    let accumulatedContent = ''
    let hasError = false
    const lastMessageTime = Date.now()
    let isDone = false
    // 添加累积工具执行结果的数组
    let accumulatedToolExecutionList: Chat.ToolExecution[] = []
    // 添加累积推理内容
    let accumulatedReasoningContent = ''
    // 添加标记，用于判断是否收到了START事件
    const hasStartEvent = false
    // 添加START事件时间戳
    const startEventTime = 0
    // SSE数据缓冲区，用于处理跨chunk的数据
    let sseBuffer = ''

    if (!reader) throw new Error('Failed to get response reader')

    // 保存当前reader引用
    state.currentReader = reader
    state.currentIndex = index

    // 设置超时管理
    this.setupTimeout(state, conversationId, index)

    /**
     * 解析SSE数据流
     * 正确处理跨chunk的SSE消息
     */
    const parseSSEData = (buffer: string): { messages: string[]; remainingBuffer: string } => {
      const messages: string[] = []
      const lines = buffer.split('\n')
      let currentMessage = ''
      let i = 0

      while (i < lines.length) {
        const line = lines[i]

        // 如果是最后一行且不以换行符结尾，可能是不完整的数据
        if (i === lines.length - 1 && !buffer.endsWith('\n')) {
          return { messages, remainingBuffer: line }
        }

        // 空行表示一个SSE消息的结束
        if (line.trim() === '') {
          if (currentMessage.trim()) {
            messages.push(currentMessage.trim())
            currentMessage = ''
          }
        } else {
          currentMessage += `${line}\n`
        }

        i++
      }

      // 如果buffer以换行符结尾，处理最后一个消息
      if (buffer.endsWith('\n') && currentMessage.trim()) {
        messages.push(currentMessage.trim())
      }

      return {
        messages,
        remainingBuffer: buffer.endsWith('\n') ? '' : currentMessage,
      }
    }

    /**
     * 提取并处理文本中的思维链内容
     * 支持两种格式的思维链：
     * 1. 代码块格式: ```thinking\n...\n```
     * 2. 标签格式: <think>...</think>
     * 3. DeepSeek R1格式: 通过reasoningContent字段输出推理
     *
     * 优化：支持流式传输中的不完整思考块
     */
    const extractThinkingChain = (value: string) => {
      const newThinkingChains: { content: string }[] = []

      // 处理代码块格式
      if (value.includes('```thinking')) {
        const parts = value.split('```')
        let hasCompleteThinkingBlock = false

        // 首先处理完整的 ```thinking...``` 块
        parts.forEach((part, index) => {
          if (part.startsWith('thinking\n') && index % 2 === 1) {
            const content = part.replace('thinking\n', '').trim()
            if (content) {
              newThinkingChains.push({ content })
              hasCompleteThinkingBlock = true
            }
          }
        })

        // 如果没有完整的thinking块，处理不完整的情况
        if (!hasCompleteThinkingBlock) {
          const thinkingIndex = value.indexOf('```thinking\n')
          if (thinkingIndex !== -1) {
            const contentAfterThinking = value.substring(thinkingIndex + 12) // 12 是 '```thinking\n' 的长度
            if (contentAfterThinking.trim()) {
              newThinkingChains.push({ content: contentAfterThinking.trim() })
            } else {
              // 如果 ```thinking 后面还没有内容，显示占位符
              newThinkingChains.push({ content: '思考中...' })
            }
          }
        }
      }

      // 处理标签格式
      if (value.includes('<think>')) {
        let hasCompleteThinkBlock = false

        // 首先处理完整的 <think>...</think> 块
        if (value.includes('</think>')) {
          const regex = /<think>([\s\S]*?)<\/think>/g
          let match = regex.exec(value)
          while (match !== null) {
            const thinkingContent = match[1].trim()
            if (thinkingContent) {
              newThinkingChains.push({ content: thinkingContent })
              hasCompleteThinkBlock = true
            }
            match = regex.exec(value)
          }
        }

        // 如果没有完整的think块，处理不完整的情况
        if (!hasCompleteThinkBlock) {
          // 直接提取 <think> 后的内容，即使还没有 </think>
          const thinkStartIndex = value.indexOf('<think>')
          if (thinkStartIndex !== -1) {
            const contentAfterThink = value.substring(thinkStartIndex + 7) // 7 是 '<think>' 的长度
            if (contentAfterThink.trim()) {
              newThinkingChains.push({ content: contentAfterThink.trim() })
            } else {
              // 如果 <think> 后面还没有内容，显示占位符
              newThinkingChains.push({ content: '思考中...' })
            }
          }
        }
      }

      return newThinkingChains
    }

    try {
      while (true) {
        try {
          const { done, value } = await reader.read()

          if (done) {
            this.cleanupConversationResources(state)
            break
          }

          // 更新最后消息时间 - 通知超时管理器
          if ((state as any).updateLastMessageTime) {
            ;(state as any).updateLastMessageTime()
          }

          const chunk = decoder.decode(value)

          // 直接检查整个chunk是否包含错误信息
          if (chunk.includes('"code":500') || chunk.includes('"code": 500')) {
            try {
              const errorData = JSON.parse(chunk)
              if (errorData.code !== 200) {
                this.processStreamError(conversationId, index, errorData.msg || '请求异常')
                return
              }
            } catch (e) {
              // 尝试直接使用整个错误信息
              const errorMatch = chunk.match(/"msg"\s*:\s*"([^"]+)"/)
              const errorMsg = errorMatch ? errorMatch[1] : '服务器内部错误'
              this.processStreamError(conversationId, index, errorMsg)
              return
            }
          }
          // resolved 可能存在401的情况 "code":401
          if (chunk.includes('"code":401') || chunk.includes('"code": 401')) {
            this.processStreamError(conversationId, index, '未授权，请重新登录')
            return
          }

          // 将新的chunk数据添加到缓冲区
          sseBuffer += chunk

          // 解析SSE数据
          const { messages, remainingBuffer } = parseSSEData(sseBuffer)
          sseBuffer = remainingBuffer

          console.log('parsed SSE messages:', messages?.length)

          // 处理每个完整的SSE消息
          for (const message of messages) {
            const lines = message.split('\n')

            for (const line of lines) {
              if (line.startsWith('event:')) {
                const event = line.substring(6).trim()
                if (event === '[ERROR]') {
                  hasError = true
                  continue
                }
                // 检测START事件
                if (event === '[START]') {
                  if ((state as any).setStartEvent) {
                    ;(state as any).setStartEvent()
                  }
                  continue
                }
                // 检测DONE事件
                if (event === '[DONE]') {
                  isDone = true
                  continue
                }
                // resolved 点击handleStop，会在sse数据流中返回[CLOSE]事件，这种情况需要处理中断数据流
                // resolved 这种情况下应该触发getSummary吗
                if (event === '[CLOSE]') {
                  // 只清理资源，不设置loading状态，避免与stopConversation冲突
                  this.cleanupConversationResourcesWithoutLoading(state)
                  reader.cancel('用户取消') // 取消读取流
                  // 更新当前消息状态
                  updateChatSome(conversationId, index, {
                    loading: false,
                  })
                  isDone = true // 标记为已完成
                  console.log('收到[CLOSE]事件，已停止数据流')
                  return // 直接返回，终止处理
                }
              } else if (line.startsWith('data:')) {
                const dataContent = line.substring(5).trim()
                if (!dataContent) continue

                try {
                  // 如果是错误事件，dataContent 就是错误消息
                  if (hasError) {
                    this.processStreamError(conversationId, index, '请求异常', dataContent)
                    return
                  }

                  const data = JSON.parse(dataContent)

                  // 第一次接收到id时，将其存入chatStore
                  if (data.id && !accumulatedContent) {
                    // 更新当前AI回复消息的id
                    // console.log('[data.id && !accumulatedContent]-data.id', data.id)
                    updateChatSome(conversationId, index, {
                      conversationOptions: {
                        conversationId: data.conversationId || conversationId,
                        chatId: data.chatId,
                        id: data.id,
                      },
                    })
                  }
                  // 如果已经收到DONE事件，只处理id信息，不再更新显示内容
                  if (isDone) {
                    // 从DONE后的消息中获取id
                    // resolved 排查一下逻辑，我发现accumulatedContent累加到最后，和data.content不一致，理论上是应该一致的
                    // console.log('[isDone]-data.id', data.id)
                    sessionStorage.setItem('streamResponse_accumulatedContent', accumulatedContent)
                    sessionStorage.setItem('streamResponse_isDone_content', data.content)
                    if (data.id) {
                      console.log('updateChatSome-isDone分支')
                      // 更新当前AI回复消息的id和最终内容
                      updateChatSome(conversationId, index, {
                        conversationOptions: {
                          conversationId: data.conversationId || conversationId,
                          chatId: data.chatId,
                          id: data.id,
                        },
                        // resolved 此时把完整内容赋值给text，在src\views\chat\components\Message\Text.vue中没有成功渲染对应data.content，渲染的依旧是之前流式累加进去的accumulatedContent
                        // resolved 目前此处会导致content里的think内容也显示在正文里
                        text: this.cleanThinkingMarkers(data.content || accumulatedContent), // 使用完整的内容替换累加内容，并清理思维链标记
                      })

                      const { isPublicChat } = usePublicChat()
                      const getChatContentRecordList = isPublicChat
                        ? getBizGuestChatContentRecordList
                        : getBizChatContentRecordList
                      // 在对话完成后获取相关的知识库引用内容
                      try {
                        const contentRecordResponse = await getChatContentRecordList<
                          Chat.ChatContentRecord[]
                        >({
                          chatPrimaryKey: data.id,
                        })

                        // 更新消息，添加chatContentRecordList
                        if (contentRecordResponse && contentRecordResponse.data) {
                          updateChatSome(conversationId, index, {
                            chatContentRecordList: contentRecordResponse.data,
                          })
                        }
                      } catch (contentError) {
                        console.error('获取知识库引用内容失败:', contentError)
                      }
                    }
                    continue
                  }
                  // 后端接入了mcp能力，现在会在接口中返回数据增加toolExecution字段，该字段含键值name arguments result
                  // 更新聊天内容
                  if (data.content !== undefined || data.reasoningContent) {
                    // 处理普通内容
                    if (data.content !== undefined) {
                      accumulatedContent += data.content
                    }

                    // 保存累积内容到状态中，用于会话切换时的状态同步
                    state.accumulatedContent = accumulatedContent
                    state.currentIndex = index

                    // 提取toolExecution字段（如果存在）并累积到数组中
                    if (data.toolExecution) {
                      const newToolExecutionList = Array.isArray(data.toolExecution)
                        ? data.toolExecution
                        : [data.toolExecution]

                      // 将新的工具执行结果添加到累积数组中
                      accumulatedToolExecutionList = [
                        ...accumulatedToolExecutionList,
                        ...newToolExecutionList,
                      ]
                    }

                    // 处理DeepSeek R1格式的推理内容
                    if (data.reasoningContent) {
                      accumulatedReasoningContent += data.reasoningContent
                    }

                    // 如果没有 DeepSeek R1 格式的推理内容，则处理 OpenAI 格式的 <think> 标签
                    if (!data.reasoningContent) {
                      // 提取思维链内容（OpenAI格式的兼容处理，支持流式传输中的不完整块）
                      const newThinkingChains = extractThinkingChain(accumulatedContent)
                      if (newThinkingChains.length > 0) {
                        // 使用提取到的思维链内容（可能是完整内容或占位符）
                        accumulatedReasoningContent = newThinkingChains
                          .map(chain => chain.content)
                          .join('\n')
                      }
                    }
                    // 保存累积推理内容到状态中，用于会话切换时的状态同步
                    state.accumulatedReasoningContent = accumulatedReasoningContent

                    // 清理显示文本中的思维链标记
                    const cleanedContent = this.cleanThinkingMarkers(accumulatedContent)

                    // 更新AI回复消息
                    updateChatSome(conversationId, index, {
                      dateTime: data.createTime || new Date().toLocaleString(),
                      text: cleanedContent,
                      inversion: false,
                      error: false,
                      loading: !data.isEnd,
                      toolExecutionList:
                        accumulatedToolExecutionList.length > 0
                          ? accumulatedToolExecutionList
                          : undefined,
                      reasoningContent: accumulatedReasoningContent,
                    })

                    // 不直接调用scrollToBottomIfAtBottom，因为组件切换时函数引用会失效
                    // UI滚动应该由组件自己处理

                    // 如果是新对话,同时更新用户消息的chatId
                    if (data.chatId && index > 0) {
                      // 获取当前用户消息，保留其原有的所有数据（包括图片信息）
                      const currentUserMessage = chatStore.getChatByConversationIdAndIndex(
                        conversationId,
                        index - 1,
                      )

                      updateChat(conversationId, index - 1, {
                        ...currentUserMessage, // 保留原有数据
                        dateTime: new Date().toLocaleString(),
                        text: userMessage,
                        inversion: true,
                        error: false,
                        conversationOptions: {
                          conversationId,
                          chatId: data.chatId,
                        },
                      })
                      // 更新最新的chatId
                      latestChatId.value = data.chatId
                    }

                    if (data.isEnd) {
                      // 标记流式响应结束，但在finally块中统一清理loading状态
                      isDone = true
                      // 不直接调用scrollToBottom，让组件自己处理滚动
                    }
                  }
                } catch (error) {
                  //
                }
              }
            }
          }
        } catch (error) {
          if (state.timeoutTimer) {
            clearInterval(state.timeoutTimer)
            state.timeoutTimer = undefined
          }
          throw error
        }
      }
    } catch (error) {
      if (state.timeoutTimer) {
        clearInterval(state.timeoutTimer)
        state.timeoutTimer = undefined
      }
      throw error
    } finally {
      // 只有在会话正常结束或出现错误时才清理loading状态
      // 避免与stopConversation产生竞态条件
      const shouldCleanupLoading = isDone || hasError || !state.loading.value

      // 清理超时定时器
      if (state.timeoutTimer) {
        clearInterval(state.timeoutTimer)
        state.timeoutTimer = undefined
      }

      // 确保在函数结束时获取摘要
      if (!hasError && isDone) {
        getSummary(conversationId)
      }

      // 清理reader引用和累积数据
      state.currentReader = null
      state.accumulatedContent = undefined
      state.accumulatedReasoningContent = undefined
      state.currentIndex = undefined

      // 统一清理loading状态
      if (shouldCleanupLoading) {
        console.log(
          `[StreamManager] coreStreamResponse finally块 将loading设置为false，会话ID: ${conversationId}，原因: isDone=${isDone}, hasError=${hasError}, 当前loading=${state.loading.value}`,
        )
        state.loading.value = false
      }
    }
  }

  // 发送请求
  async sendRequest(
    conversationId: string,
    userMessage: string,
    latestChatId: string | undefined,
    onStreamResponse: (response: Response, index: number, message: string) => Promise<void>,
    images?: Array<{ url: string; fileName: string; ossId: string }>,
  ): Promise<void> {
    const state = this.getOrCreateState(conversationId)
    const chatStore = useChatStore()

    // console.log(`[sendRequest] 会话 ${conversationId} 当前loading状态:`, state.loading.value)
    if (state.loading.value) return

    // 清理之前可能残留的状态，确保干净的开始
    this.cleanupConversationResourcesWithoutLoading(state)

    // console.log(`[sendRequest] 设置会话 ${conversationId} loading状态为true`)
    state.loading.value = true

    // 确保controller存在
    if (!state.controller) {
      state.controller = new AbortController()
    }

    try {
      const requestData: any = {
        conversationId,
        userMessage,
        chatId: latestChatId,
      }

      // 如果有图片，添加到请求数据中
      if (images && images.length > 0) {
        requestData.hasImages = true
        requestData.imageInfoList = images.map(img => ({
          imageUrl: img.url,
          ossId: img.ossId,
        }))
        console.log('发送图片数据:', requestData.imageInfoList)
      } else {
        requestData.hasImages = false
      }
      // 使用动态token获取，支持助手特定的token
      const token = getCurrentToken()
      const { isPublicChat } = usePublicChat()
      const conversationUrl = isPublicChat
        ? `${import.meta.env.VITE_APP_BASE_API}/biz/guest/conversation/completions`
        : `${import.meta.env.VITE_APP_BASE_API}/biz/conversation/completions`
      const response = await fetch(conversationUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          Clientid: import.meta.env.VITE_APP_CLIENT_ID,
        },
        body: JSON.stringify(requestData),
        signal: state.controller.signal,
      })

      await onStreamResponse(
        response,
        chatStore.getChatByConversationId(conversationId).length - 1,
        userMessage,
      )
    } catch (error: any) {
      const errorMessage = error?.message ?? t('common.wrong')
      if (error.message === 'canceled') {
        const lastIndex = chatStore.getChatByConversationId(conversationId).length - 1
        const chatData = chatStore.getChatByConversationId(conversationId)[lastIndex]
        const { updateChat } = useChat()

        updateChat(conversationId, lastIndex, {
          ...chatData,
          loading: false,
        })
        return
      }

      const { updateChat, getChatByConversationIdAndIndex } = useChat()
      const currentChat = getChatByConversationIdAndIndex(
        conversationId,
        chatStore.getChatByConversationId(conversationId).length - 1,
      )
      if (currentChat?.text === t('chat.thinking')) {
        const lastIndex = chatStore.getChatByConversationId(conversationId).length - 1
        // 保存原有的conversationOptions，避免丢失消息id
        const originalConversationOptions =
          chatStore.getChatByConversationId(conversationId)[lastIndex]?.conversationOptions

        updateChat(conversationId, lastIndex, {
          dateTime: new Date().toLocaleString(),
          text: errorMessage,
          inversion: false,
          error: true,
          loading: false,
          conversationOptions: originalConversationOptions, // 保持原有的conversationOptions，确保id不丢失
        })
      }
    } finally {
      state.loading.value = false
    }
  }

  // 重试请求
  async retryRequest(
    conversationId: string,
    index: number,
    latestChatId: string | undefined,
    onStreamResponse: (response: Response, index: number, message: string) => Promise<void>,
  ): Promise<void> {
    const state = this.getOrCreateState(conversationId)
    const chatStore = useChatStore()

    if (state.loading.value) return

    // 清理之前可能残留的状态，确保干净的开始
    this.cleanupConversationResourcesWithoutLoading(state)

    const userChatData = chatStore.getChatByConversationId(conversationId)[index - 1]
    const userMessage = userChatData?.text ?? ''
    const userImages = userChatData?.imageInfoList
    const id =
      chatStore.getChatByConversationId(conversationId)[index]?.conversationOptions?.id ?? ''

    // 检查是否有图片但当前模型不支持视觉 - 在开始处理前进行判断
    if (userImages && userImages.length > 0) {
      let currentModelSupportsVision = false
      try {
        const { data: modelList } = await getBizAiModelAvailableModels()
        const currentModelId = chatStore.modelId
        const currentModel = modelList.find(model => model.id === String(currentModelId))
        currentModelSupportsVision = currentModel?.capabilities?.includes('vision') || false
      } catch (error) {
        console.warn('获取模型列表失败，默认不支持视觉:', error)
        currentModelSupportsVision = false
      }

      if (!currentModelSupportsVision) {
        // 弹窗提示用户当前模型不支持视觉识别
        ElMessage.warning('当前模型不支持视觉识别，请重新选择支持视觉的模型')
        // 直接返回，不修改任何消息状态
        return
      }
    }

    state.loading.value = true

    // 确保controller存在
    if (!state.controller) {
      state.controller = new AbortController()
    }

    const { updateChat } = useChat()
    // 保存原有的conversationOptions，避免丢失消息id
    const originalConversationOptions =
      chatStore.getChatByConversationId(conversationId)[index]?.conversationOptions
    updateChat(conversationId, index, {
      dateTime: new Date().toLocaleString(),
      text: t('chat.thinking'),
      inversion: false,
      error: false,
      loading: true,
      conversationOptions: originalConversationOptions,
    })

    try {
      const requestData: any = {
        id,
        conversationId,
        userMessage,
        chatId: latestChatId,
      }

      // 如果原始用户消息包含图片，添加到重试请求中（前面已经检查过模型支持性）
      if (userImages && userImages.length > 0) {
        requestData.hasImages = true
        requestData.imageInfoList = userImages.map(img => ({
          imageUrl: img.imageUrl,
        }))
        console.log('重试请求包含图片数据:', requestData.imageInfoList)
      } else {
        requestData.hasImages = false
      }
      // 使用动态token获取，支持助手特定的token
      const token = getCurrentToken()
      const { isPublicChat } = usePublicChat()
      const retryUrl = isPublicChat
        ? `${import.meta.env.VITE_APP_BASE_API}/biz/guest/conversation/retry`
        : `${import.meta.env.VITE_APP_BASE_API}/biz/conversation/retry`
      // resolved 重试请求可能接口500报错，那么此时应该保持原有的消息id，避免无法再次重试请求
      const response = await fetch(retryUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
          Clientid: import.meta.env.VITE_APP_CLIENT_ID,
        },
        body: JSON.stringify(requestData),
        signal: state.controller.signal,
      })

      await onStreamResponse(response, index, userMessage)
    } catch (error: any) {
      const errorMessage = error?.message ?? t('common.wrong')
      if (error.message === 'canceled') {
        const chatData = chatStore.getChatByConversationId(conversationId)[index]

        updateChat(conversationId, index, {
          ...chatData,
          loading: false,
        })
        return
      }

      updateChat(conversationId, index, {
        dateTime: new Date().toLocaleString(),
        text: errorMessage,
        inversion: false,
        error: true,
        loading: false,
        conversationOptions: originalConversationOptions, // 保持原有的conversationOptions，确保id不丢失
      })
    } finally {
      state.loading.value = false
    }
  }

  // 清理指定会话的状态
  clearState(conversationId: string): void {
    const state = this.getState(conversationId)
    if (state) {
      this.cleanupConversationResources(state)
    }
    this.conversationStates.delete(conversationId)
  }

  // 清理所有状态
  clearAllStates(): void {
    for (const [conversationId] of this.conversationStates) {
      this.clearState(conversationId)
    }
  }

  // 获取所有正在加载的会话
  getLoadingConversations(): string[] {
    const loading: string[] = []
    for (const [conversationId, state] of this.conversationStates) {
      if (state.loading.value) {
        loading.push(conversationId)
      }
    }
    return loading
  }

  // 恢复会话状态，用于组件重新挂载时同步状态
  restoreConversationState(conversationId: string): {
    isLoading: boolean
    restoreData?: {
      content: string
      messageIndex: number
      reasoningContent?: string
    }
  } {
    const state = this.getState(conversationId)
    if (!state) {
      return { isLoading: false }
    }

    const restoreData =
      state.accumulatedContent && state.currentIndex !== undefined
        ? {
            // resolved 为什么不在这里处理掉呢 - 确实可以在这里统一处理，简化调用方逻辑
            content: this.cleanThinkingMarkers(state.accumulatedContent),
            messageIndex: state.currentIndex,
            reasoningContent: state.accumulatedReasoningContent,
          }
        : undefined

    return {
      isLoading: state.loading.value,
      restoreData,
    }
  }

  /**
   * 清理文本中的思维链标记
   * 统一的清理逻辑，避免代码重复
   */
  private cleanThinkingMarkers(value: string) {
    let processedValue = value

    // 移除代码块格式的思维链
    if (processedValue.includes('```thinking')) {
      // 先移除完整的 ```thinking...``` 块
      const parts = processedValue.split('```')
      processedValue = parts
        .filter((part, index) => {
          return !(index % 2 === 1 && part.startsWith('thinking\n'))
        })
        .join('')

      // 处理不完整的 ```thinking 块（流式传输中可能只有开始标记）
      if (
        processedValue.includes('```thinking') &&
        !processedValue.includes('```', processedValue.indexOf('```thinking') + 12)
      ) {
        const thinkingIndex = processedValue.indexOf('```thinking')
        processedValue = processedValue.substring(0, thinkingIndex)
      }
    }

    // 移除标签格式的思维链
    if (processedValue.includes('<think>')) {
      // 先移除完整的 <think>...</think> 块
      processedValue = processedValue.replace(/<think>[\s\S]*?<\/think>/g, '')

      // 处理不完整的 <think> 块（流式传输中可能只有开始标签）
      // 如果还存在 <think> 但没有对应的 </think>，移除从 <think> 开始的所有内容
      if (processedValue.includes('<think>') && !processedValue.includes('</think>')) {
        const thinkIndex = processedValue.indexOf('<think>')
        processedValue = processedValue.substring(0, thinkIndex)
      }
    }

    return processedValue
  }

  // 获取loading状态的ref，用于Vue组件中的响应式追踪
  getLoadingRef(conversationId: string): Ref<boolean> {
    const state = this.getOrCreateState(conversationId)
    return state.loading
  }

  // 获取当前正在处理的消息信息（用于handleStop时获取正确的消息id）
  getCurrentProcessingMessage(
    conversationId: string,
  ): { index: number; id: string | undefined } | null {
    const state = this.getState(conversationId)
    if (!state || !state.loading.value || state.currentIndex === undefined) {
      return null
    }

    const chatStore = useChatStore()
    const chatData = chatStore.getChatByConversationId(conversationId)

    if (state.currentIndex >= 0 && state.currentIndex < chatData.length) {
      const currentMessage = chatData[state.currentIndex]
      return {
        index: state.currentIndex,
        id: currentMessage?.conversationOptions?.id,
      }
    }

    return null
  }
}

// 创建全局单例实例
export const streamResponseManager = new StreamResponseManager()
