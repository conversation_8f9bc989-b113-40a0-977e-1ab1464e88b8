/**
 * 对话信息
 */
export interface ChatVo {
  id: string
  title?: string
  createTime?: string
  updateTime?: string
  // 其他对话字段
}

/**
 * 对话记录项
 */
export interface ChatRecordItem {
  id: string
  content: string
  role: 'user' | 'assistant'
  createTime: string
}

/**
 * 更新对话参数
 */
export interface UpdateChatParams {
  id: string
  title?: string
  // 其他可更新字段
}

/**
 * 获取对话记录参数
 */
export interface GetChatRecordParams {
  conversationId: number;
  firstNum?: number;
  /**
   * 排序的方向desc或者asc
   */
  isAsc?: string;
  /**
   * 排序列
   */
  orderByColumn?: string;
  /**
   * 当前页数
   */
  pageNum?: number;
  /**
   * 分页大小
   */
  pageSize?: number;
}

/**
 * 会话记录列表参数
 */
export interface ChatRecordListParams {
  firstNum?: number;
  /**
   * 排序的方向desc或者asc
   */
  isAsc?: string;
  keyword: string;
  /**
   * 排序列
   */
  orderByColumn?: string;
  /**
   * 当前页数
   */
  pageNum?: number;
  /**
   * 分页大小
   */
  pageSize?: number;
}

/**
 * 会话记录列表 返回数据
 */
export interface ChatRecordListResultModel {
  /**
    * 所属助手背景色
    */
  agentBackgroundColor?: string;
  /**
   * 所属助手图标
   */
  agentEmoji?: string;
  /**
   * 所属助手
   */
  agentId?: number;
  /**
   * 所属助手名称
   */
  agentName?: string;
  /**
   * 头像地址
   */
  avatar?: number;
  /**
   * 创建者
   */
  createBy?: number;
  /**
   * 创建时间
   */
  createTime?: Date;
  id?: number;
  /**
   * 是否开启知识库
   */
  kbEnableFlag?: boolean;
  /**
   * 关联知识库列表
   */
  kbList?: KnowledgeBaseVo[];
  /**
   * 最大上下文数量
   */
  maxContextCount?: number;
  /**
   * 最大召回数
   */
  maxResults?: number;
  /**
   * 最大回复长度
   */
  maxTokens?: number;
  /**
   * 最低匹配度
   */
  minScore?: number;
  /**
   * 模型显示名称
   */
  modelDisplayName?: string;
  /**
   * 模型id
   */
  modelId?: number;
  /**
   * 模型name
   */
  modelName?: string;
  /**
   * 名称
   */
  name?: string;
  /**
   * 用户昵称
   */
  nickName?: string;
  /**
   * rerank模型id
   */
  rerankModelId?: number;
  /**
   * rerank模型id
   */
  rerankModelName?: string;
  /**
   * 开启单次回复限制
   */
  singleReplyLimitFlag?: boolean;
  /**
   * 副标题
   */
  subtitle?: string;
  /**
   * 系统提示词
   */
  systemMessage?: string;
  /**
   * 随机性
   */
  temperature?: number;
  /**
   * 消耗的token数量
   */
  tokens?: number;
  /**
   * 更新时间
   */
  updateTime?: Date;
}

/**
 * KnowledgeBaseVo
 */
export interface KnowledgeBaseVo {
  /**
   * 描述
   */
  description?: string;
  /**
   * 编辑时间
   */
  editTime?: Date;
  /**
   * 是否启用
   */
  enabled?: boolean;
  /**
   * 主键
   */
  id?: number;
  /**
   * 模型是否还在
   * true 在
   * false 不在
   */
  modelExist?: boolean;
  /**
   * 模型id
   */
  modelId?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 权限列表
   */
  permissionList?: KnowledgeBasePermission[];
  /**
   * 分享权限等级 1-仅查看 2-可编辑
   */
  sharePermissionLevel?: string;
  /**
   * 分享状态 0-私人 1-全体共享 2-指定部门共享 3-指定成员共享
   */
  shareStatus?: number;
  /**
   * 大小
   */
  size?: number;
  /**
   * 类型 1-文本 2-图像
   */
  type?: number;
  /**
   * 所有者
   */
  userId?: number;
  /**
   * 所有者名称
   */
  userName?: string;
  [property: string]: any;
}

/**
 * KnowledgeBasePermission，知识库部门共享权限关联
 */
export interface KnowledgeBasePermission {
  /**
   * 知识库id
   */
  kbId?: number;
  /**
   * 0-不可见 1-仅查看 2-可编辑
   */
  permissionLevel?: number;
  /**
   * 权限目标 ID
   */
  targetId?: number;
  /**
   * 权限目标类型 1-部门 2-用户
   */
  targetType?: string;
}
