# 阿里云云效部署解决方案

## 问题描述
在阿里云云效流水线部署时，遇到 electron 包安装失败的问题：
```
npm error code 1
npm error path /opt/buildagent/work/c519653258520e62/node_modules/electron
npm error command failed
npm error command sh -c node install.js
npm error ReadError: The server aborted pending request
```

## 解决方案

### 方案一：使用专用构建脚本（推荐）

在云效流水线中使用以下命令：
```bash
node build-for-ci.js
```

这个脚本会：
1. 跳过 electron 二进制文件下载
2. 使用国内镜像源
3. 使用简化的依赖列表
4. 自动处理构建流程

### 方案二：使用 Shell 脚本

在云效流水线中使用：
```bash
chmod +x ci-build.sh
./ci-build.sh
```

### 方案三：手动配置流水线

在云效流水线的构建步骤中添加：

```bash
# 设置环境变量
export ELECTRON_SKIP_BINARY_DOWNLOAD=1
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

# 设置镜像源
npm config set registry https://registry.npmmirror.com/
npm config set electron_mirror https://npmmirror.com/mirrors/electron/

# 使用云效专用配置
cp package.ci.json package.json

# 安装依赖并构建
npm install --production=false --ignore-scripts
npm run build:stage
```

## 配置文件说明

### .npmrc
已更新为包含国内镜像源配置，解决网络问题。

### package.ci.json
专门用于云效部署的简化版 package.json，移除了 electron 相关依赖。

### .codeup-ci.yml
云效流水线配置文件，包含完整的构建和部署流程。

## 注意事项

1. **不要删除原始的 package.json**：本地开发仍需要完整的依赖列表
2. **构建脚本会自动备份和恢复**：确保本地开发环境不受影响
3. **只在云效环境使用**：这些配置专门针对云效部署优化

## 验证部署

构建成功后，检查 `dist` 目录是否包含以下文件：
- index.html
- assets/ 目录
- build-info.json

## 故障排除

如果仍然遇到问题：

1. 检查云效环境的 Node.js 版本（建议使用 Node 16+）
2. 确认网络连接正常
3. 查看是否有其他依赖包下载失败
4. 考虑使用 `npm ci` 替代 `npm install`

## 联系支持

如果问题持续存在，请提供：
- 完整的错误日志
- 云效环境配置信息
- Node.js 和 npm 版本信息
