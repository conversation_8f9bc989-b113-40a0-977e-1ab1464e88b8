<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { postBizTokenUsageTimeSeries } from '@/api/usage'
import type {
  TokenUsageTimeSeriesQueryBo,
  TokenUsageAggregatePageQueryBo,
  TimeSeriesDataPoint,
  AggregateDataPoint,
  TokenUsageTimeSeriesVo,
} from '@/api/usage/types'
import { StatType } from '@/api/usage/types'
import dayjs from 'dayjs'
import TokenStats from './components/TokenStats.vue'
import UsageChart from './components/UsageChart.vue'
import UsageQuery from './components/UsageQuery.vue'
import { formatToken } from '@/utils/formatter'
import { useTableSort } from './hooks/useTableSort'
import ModelInvocationDialog from './components/ModelInvocationDialog/index.vue'
import MemberInvocationDialog from './components/MemberInvocationDialog.vue'
import OutwardInvocationDialog from './components/OutwardInvocationDialog.vue'
import { useAggregatePage } from './hooks/useAggregatePage'

/**
 * 使用统计标签页功能
 * 管理当前选中的标签页和标签页列表
 */
function useUsageTab() {
  const currentTab = ref<StatType>(StatType.Model)
  const usageTabList = [
    {
      label: '模型统计',
      value: StatType.Model,
    },
    {
      label: '成员统计',
      value: StatType.User,
    },
    {
      label: '应用统计',
      value: StatType.Outward,
    },
  ]
  return {
    currentTab,
    usageTabList,
  }
}
const { currentTab, usageTabList } = useUsageTab()

/**
 * 计算表格列标签
 * 根据当前选中的标签页类型动态返回对应的列标签
 */
const columnLabels = computed(() => {
  switch (currentTab.value) {
    case 'MODEL':
      return {
        entityNameLabel: '模型',
        groupNameLabel: '平台',
      }
    case 'USER':
      return {
        entityNameLabel: '姓名',
        groupNameLabel: '部门',
      }
    case 'OUTWARD':
      return {
        entityNameLabel: '名称',
        groupNameLabel: '关联助手',
      }
    default:
      return {
        entityNameLabel: '名称',
        groupNameLabel: '分组',
      }
  }
})

/**
 * 使用日期范围功能
 * 管理时间范围选择和格式化
 */
function useDateRange() {
  const now = dayjs()
  const dateRange = ref<[Date, Date]>([now.subtract(15, 'day').toDate(), now.toDate()])

  const startTime = computed(() => dayjs(dateRange.value[0]).format('YYYY-MM-DD 00:00:00'))
  const endTime = computed(() => dayjs(dateRange.value[1]).format('YYYY-MM-DD 23:59:59'))

  return {
    dateRange,
    startTime,
    endTime,
  }
}

/**
 * 使用用量查询功能
 * 管理查询条件，包括时间范围、粒度、模型、部门、用户、应用等筛选条件
 */
function useUsageQuery() {
  const { dateRange, startTime, endTime } = useDateRange()

  // 时间粒度：小时/天
  const granularity = ref<'HOUR' | 'DAY'>('DAY')

  // 模型选择相关
  const selectedModel = ref('')

  // 部门和用户选择相关
  const selectedDept = ref('')
  const selectedUser = ref('')
  const selectedOutward = ref('')

  /**
   * 重置查询条件
   */
  function resetQuery() {
    selectedModel.value = ''
    selectedDept.value = ''
    selectedUser.value = ''
    selectedOutward.value = ''
  }

  return {
    dateRange,
    startTime,
    endTime,
    granularity,
    selectedModel,
    selectedDept,
    selectedUser,
    selectedOutward,
    resetQuery,
  }
}

const {
  dateRange,
  startTime,
  endTime,
  granularity,
  selectedModel,
  selectedDept,
  selectedUser,
  selectedOutward,
  resetQuery,
} = useUsageQuery()

/**
 * 处理标签页切换
 * 切换标签页时重置排序和查询条件，并重新获取数据
 */
const handleTabChange = () => {
  resetSort()
  resetQuery()
  handleQueryChange()
}

// 当前选中的Token统计标签页：总计/输入/输出
const selectedTokenStat = ref<'total' | 'input' | 'output'>('total')

// 使用聚合页面功能，获取表格数据和分页信息
const { tableRef, loading, tableData, currentPage, pageSize, total, getTokenUsageAggregatePage } =
  useAggregatePage()

// 使用表格排序功能
const { sortField, sortOrder, sortChange, resetSort } = useTableSort(() => {
  currentPage.value = 1
  getAggregateTableData()
}, tableRef)

/**
 * 使用时间序列功能
 * 管理时间序列数据的获取和状态
 */
function useTimeSeries() {
  const loading = ref(false)
  const timeSeries = ref<TokenUsageTimeSeriesVo>({
    dataPoints: [],
    summary: {
      totalTokens: 0,
      inputTokens: 0,
      outputTokens: 0,
      chatCount: 0,
      totalCost: 0,
    },
  })
  function getTimeSeries(params: TokenUsageTimeSeriesQueryBo) {
    loading.value = true
    postBizTokenUsageTimeSeries(params)
      .then(res => {
        if (res.code === 200) {
          timeSeries.value = res.data
        }
      })
      .catch(error => {
        console.error('获取时间序列数据失败:', error)
      })
      .finally(() => {
        loading.value = false
      })
  }

  return {
    loading,
    timeSeries,
    getTimeSeries,
  }
}

const { timeSeries, getTimeSeries } = useTimeSeries()

/**
 * 计算图表数据
 * 根据时间序列数据和选中的统计类型生成图表需要的数据格式
 */
const chartData = computed(() => {
  const dataPoints = timeSeries.value.dataPoints
  // 根据选中的标签页选择不同的字段来填充图表数据
  const tokenField =
    selectedTokenStat.value === 'input'
      ? 'inputTokens'
      : selectedTokenStat.value === 'output'
        ? 'outputTokens'
        : 'totalTokens'

  return {
    xAxis: {
      data: dataPoints.map((point: TimeSeriesDataPoint) => {
        const date = dayjs(point.time).format('MM/DD')
        if (granularity.value === 'DAY') {
          return date
        } else {
          const lastHour = dayjs(point.time).subtract(1, 'h').format('HH:mm')
          const currentHour = dayjs(point.time).format('HH:mm')
          return `${date} ${lastHour}-${currentHour}`
        }
      }),
    },
    series: [
      {
        data: dataPoints.map((point: TimeSeriesDataPoint) => point[tokenField]),
      },
    ],
  }
})

// 计算各类Token总数
const totalTokens = computed(() => timeSeries.value.summary.totalTokens)
const inputTokens = computed(() => timeSeries.value.summary.inputTokens)
const outputTokens = computed(() => timeSeries.value.summary.outputTokens)

/**
 * 获取时间序列数据
 * 根据当前查询条件获取时间序列数据
 */
const getTimeSeriesData = () => {
  if (!dateRange.value) return
  const params: TokenUsageTimeSeriesQueryBo = {
    startTime: startTime.value,
    endTime: endTime.value,
    granularity: granularity.value,
    modelIds: selectedModel.value ? [selectedModel.value] : undefined,
    userIds: selectedUser.value ? [selectedUser.value] : undefined,
    deptIds: selectedDept.value ? [selectedDept.value] : undefined,
    outwardIds: selectedOutward.value ? [selectedOutward.value] : undefined,
    statType: currentTab.value,
  }

  if (selectedModel.value) {
    params.modelIds = [selectedModel.value]
  }

  getTimeSeries(params)
}

/**
 * 获取聚合表格数据
 * 根据当前查询条件获取聚合表格数据，支持分页和排序
 */
const getAggregateTableData = async () => {
  if (!dateRange.value) return
  const params: TokenUsageAggregatePageQueryBo = {
    startTime: startTime.value,
    endTime: endTime.value,
    aggregateType: currentTab.value, // 默认按模型分组
    modelIds: selectedModel.value ? [selectedModel.value] : undefined,
    userIds: selectedUser.value ? [selectedUser.value] : undefined,
    deptIds: selectedDept.value ? [selectedDept.value] : undefined,
    outwardIds: selectedOutward.value ? [selectedOutward.value] : undefined,
    pageNum: currentPage.value,
    pageSize: pageSize.value,
  }
  if (sortOrder.value && sortField.value) {
    params.sortOrder = sortOrder.value
    params.sortBy = sortField.value
  }
  getTokenUsageAggregatePage(params)
}

/**
 * 处理分页大小变化
 */
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getAggregateTableData()
}

/**
 * 初始化数据
 * 设置默认日期范围并获取初始数据
 */
const initData = () => {
  // 设置默认日期范围为最近15天
  const endDate = dayjs().endOf('day').toDate()
  const startDate = dayjs().subtract(15, 'day').startOf('day').toDate()

  dateRange.value = [startDate, endDate]

  // 获取数据
  getTimeSeriesData()
  getAggregateTableData()
}

// 监听 selectedTokenStat 的变化，更新图表数据
watch(selectedTokenStat, () => {
  getTimeSeriesData()
})

// 组件挂载时初始化数据
onMounted(() => {
  initData()
})

// 各类详情对话框的引用
const modelInvocationDialogRef = ref()
const memberInvocationDialogRef = ref()
const outwardInvocationDialogRef = ref()

/**
 * 查看模型详情
 * 根据当前标签页类型打开对应的详情对话框
 */
function openModelInvocationDialog(row: AggregateDataPoint) {
  const startDate = dayjs(dateRange.value[0]).format('YYYY-MM-DD')
  const endDate = dayjs(dateRange.value[1]).format('YYYY-MM-DD')
  switch (currentTab.value) {
    case 'MODEL':
      // 需要传入 模型名称、平台名称、时间、输入 tokens、输出tokens、调用总量 tokens
      if (modelInvocationDialogRef.value) {
        // 传递数据点和时间范围
        modelInvocationDialogRef.value.editData(row, startDate, endDate)
      }
      break
    case 'USER':
      if (memberInvocationDialogRef.value) {
        // 传递数据点和时间范围
        memberInvocationDialogRef.value.editData(row, startDate, endDate)
      }
      break
    case 'OUTWARD':
      if (outwardInvocationDialogRef.value) {
        // 传递数据点和时间范围
        outwardInvocationDialogRef.value.editData(row, startDate, endDate)
      }
      break
  }
}

/**
 * 从第一页开始查询表格数据
 */
function queryTableDataFromFirstPage() {
  currentPage.value = 1
  getAggregateTableData()
}

/**
 * 处理查询条件变化
 * 根据变化的字段决定是否需要重新获取数据
 */
function handleQueryChange(filed?: string) {
  if (filed && ['granularity'].includes(filed)) {
    // 如果只是粒度变化，只更新时间序列数据
    getTimeSeriesData()
  } else {
    // 其他查询条件变化，更新时间序列数据和表格数据
    getTimeSeriesData()
    queryTableDataFromFirstPage()
  }
}
</script>

<template>
  <!-- 用量统计页面容器 -->
  <div class="usage-container">
    <!-- 统计类型标签页 -->
    <el-tabs class="usage-tabs" v-model="currentTab" @tab-change="handleTabChange">
      <el-tab-pane v-for="tab in usageTabList" :label="tab.label" :name="tab.value"></el-tab-pane>
    </el-tabs>

    <!-- 统计图表和表格区域 -->
    <div class="usage-content">
      <!-- 图表卡片 -->
      <div class="usage-chart-card">
        <!-- 查询条件组件 -->
        <UsageQuery
          :current-tab="currentTab"
          v-model:date-range="dateRange"
          v-model:granularity="granularity"
          v-model:selected-model="selectedModel"
          v-model:selected-dept="selectedDept"
          v-model:selected-user="selectedUser"
          v-model:selected-outward="selectedOutward"
          @change="handleQueryChange"
        />

        <!-- Token统计组件 -->
        <TokenStats
          v-model="selectedTokenStat"
          :total-tokens="totalTokens"
          :input-tokens="inputTokens"
          :output-tokens="outputTokens"
        />

        <!-- 图表容器 -->
        <div class="chart-container">
          <UsageChart :x-axis-data="chartData.xAxis.data" :series-data="chartData.series[0].data" />
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="useage-table">
        <!-- 数据表格 -->
        <el-table
          ref="tableRef"
          :data="tableData"
          v-loading="loading"
          @sort-change="sortChange"
          style="width: 100%"
          row-class-name="usage-table-row"
          @row-click="openModelInvocationDialog"
        >
          <!-- 序号列 -->
          <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>

          <!-- 实体名称列（根据标签页类型动态显示） -->
          <el-table-column
            prop="entityName"
            :label="columnLabels.entityNameLabel"
            show-overflow-tooltip
          ></el-table-column>

          <!-- 分组名称列（根据标签页类型动态显示） -->
          <el-table-column
            prop="groupName"
            :label="columnLabels.groupNameLabel"
            show-overflow-tooltip
          ></el-table-column>

          <!-- 输入Tokens数列 -->
          <el-table-column prop="inputTokens" label="输入Tokens数" sortable="custom">
            <template #default="{ row }">
              <span>{{ `${formatToken(row.inputTokens)}` }}</span>
            </template>
          </el-table-column>

          <!-- 输出Tokens数列 -->
          <el-table-column prop="outputTokens" label="输出Tokens数" sortable="custom">
            <template #default="{ row }">
              <span>{{ `${formatToken(row.outputTokens)}` }}</span>
            </template>
          </el-table-column>

          <!-- 调用总量Tokens数列 -->
          <el-table-column
            prop="totalTokens"
            label="调用总量Tokens数"
            sortable="custom"
            width="180"
          >
            <template #default="{ row }">
              <span>{{ `${formatToken(row.totalTokens)}` }}</span>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <!--  @click="openModelInvocationDialog(row)" -->
          <el-table-column label="操作" width="100" align="center">
            <template #default="{ row }">
              <el-button link type="primary" size="small">查看</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :small="false"
            :disabled="false"
            :background="false"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="getAggregateTableData"
          />
        </div>
      </div>
    </div>
  </div>

  <!-- 各类详情对话框组件 -->
  <ModelInvocationDialog ref="modelInvocationDialogRef" />
  <MemberInvocationDialog ref="memberInvocationDialogRef" />
  <OutwardInvocationDialog ref="outwardInvocationDialogRef" />
</template>

<style lang="less" scoped>
/** 用量统计页面样式 */

/* 页面容器 */
.usage-container {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 标签页样式调整 */
.usage-tabs:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

/* 内容区域 */
.usage-content {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  margin-right: -24px;
  padding-right: 24px;
}

/* 图表卡片高度变量 */
@card-height: 580px;

/* 图表卡片 */
.usage-chart-card {
  padding: 24px;
  background: #ffffff;
  border-radius: 8px;
  height: @card-height;

  /* 统计类型标签容器 */
  .useage-type-tab-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    background-color: #fafbff;
    margin-bottom: 24px;

    /* 统计类型标签 */
    .useage-type-tab {
      border-radius: 8px;
      background-color: #fafbff;
      padding: 18px 24px;
      cursor: pointer;

      /* 标签标题 */
      .useage-type-tab-title {
        font-size: 18px;
      }

      /* 标签数值 */
      .useage-type-tab-value {
        font-size: 32px;
        color: #4c5cec;
      }

      /* 激活状态 */
      &.active {
        position: relative;
        background-color: #fff;
        &::before {
          content: '';
          display: block;
          height: 4px;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          background-color: #4865e8;
          border-top-left-radius: 50px;
        }
      }
    }
  }

  /* 图表容器 */
  .chart-container {
    height: 317px; /* 设置图表高度 */
    width: 100%;
  }
}

/* 表格区域 */
.useage-table {
  margin-top: 24px;
  height: calc(100% - 24px - @card-height);
}

/* 分页容器 */
.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
}
</style>
<style>
.usage-table-row {
  cursor: pointer;
}
</style>
