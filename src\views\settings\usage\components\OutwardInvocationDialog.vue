<script lang="ts" setup>
import { ref, watch } from 'vue'
import { formatToken } from '@/utils/formatter'
import type { TokenUsageAggregatePageQueryBo, AggregateDataPoint } from '@/api/usage/types'
import { useTableSort } from '../hooks/useTableSort'
import { useAggregatePage } from '../hooks/useAggregatePage'
import { getModelLogo } from '@/utils/models'
import { getOutwardIcon } from '@/utils/external-release'
import dayjs from 'dayjs'
import { OutwardType } from '@/api/external-release/type'

defineOptions({
  name: 'OutwardInvocationDialog',
})

// 控制对话框显示/隐藏
const dialogVisible = defineModel({ default: false })

// 编辑数据相关
const info = ref<Partial<AggregateDataPoint>>({})
const outwardType = computed(() => info.value.type as OutwardType)
const outwardId = computed(() => info.value.entityId)
const outwardName = computed(() => info.value.entityName)
const inputTokens = computed(() => info.value.inputTokens || 0)
const outputTokens = computed(() => info.value.outputTokens || 0)
const totalTokens = computed(() => info.value.totalTokens || 0)
const timeRange = ref({ startTime: '', endTime: '' })

// 使用 useAggregatePage hook
const {
  tableRef,
  loading,
  tableData,
  currentPage: pageNum,
  pageSize,
  total,
  getTokenUsageAggregatePage,
} = useAggregatePage()

// 编辑方法，接收模型名称、平台名称、时间、输入tokens、输出tokens、调用总量tokens
function editData(dataPoint: AggregateDataPoint, startTime: string, endTime: string) {
  info.value = dataPoint
  timeRange.value = { startTime, endTime }
  openDialog()
}

// 打开对话框的方法
function openDialog() {
  dialogVisible.value = true
}

// 关闭对话框的方法
function closeDialog() {
  dialogVisible.value = false
  // 重置数据到初始状态
  timeRange.value = { startTime: '', endTime: '' }
  tableData.value = []
  total.value = 0
  pageNum.value = 1
  resetSort()
}

const { sortField, sortOrder, sortChange, resetSort } = useTableSort(fetchTableData, tableRef)

// 获取表格数据
async function fetchTableData() {
  const params: TokenUsageAggregatePageQueryBo = {
    startTime: dayjs(timeRange.value.startTime).format('YYYY-MM-DD 00:00:00'),
    endTime: dayjs(timeRange.value.endTime).format('YYYY-MM-DD 23:59:59'),
    aggregateType: 'MODEL',
    outwardIds: [outwardId.value!],
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  }

  if (sortField.value && sortOrder.value) {
    params.sortBy = sortField.value
    params.sortOrder = sortOrder.value
  }

  await getTokenUsageAggregatePage(params)
}

watch(dialogVisible, val => {
  if (val) {
    fetchTableData()
  }
})

// 处理分页变化
function handleCurrentChange(val: number) {
  pageNum.value = val
  fetchTableData()
}

// 处理页面大小变化
function handleSizeChange(val: number) {
  pageSize.value = val
  pageNum.value = 1
  fetchTableData()
}

defineExpose({
  editData,
})
</script>

<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="950"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog"
    >
      <template #header>
        <span class="title g-family-medium">应用调用量明细</span>
      </template>
      <div class="model-invocation">
        <div class="model-invocation-header">
          <div class="model-invocation-name">
            <img
              v-if="outwardType"
              class="size-10 mr-3 rounded-lg"
              :src="getOutwardIcon(outwardType)"
              alt="应用logo"
            />
            <span class="g-family-medium">{{ outwardName }}</span>
          </div>
          <div class="model-invocation-time">
            {{ timeRange.startTime }} 至 {{ timeRange.endTime }}
          </div>
        </div>
        <div class="model-invocation-content">
          <div>
            <div class="model-invocation-title">输入Tokens</div>
            <div class="model-invocation-num">{{ formatToken(inputTokens) }}</div>
          </div>
          <div>
            <div class="model-invocation-title">输出Tokens</div>
            <div class="model-invocation-num">{{ formatToken(outputTokens) }}</div>
          </div>

          <div>
            <div class="model-invocation-title">调用总量Tokens</div>
            <div class="model-invocation-num">{{ formatToken(totalTokens) }}</div>
          </div>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table
        ref="tableRef"
        :data="tableData"
        @sort-change="sortChange"
        v-loading="loading"
        style="width: 100%; margin-top: 20px; min-height: 350px"
      >
        <el-table-column type="index" label="序号" width="80" align="center"> </el-table-column>
        <el-table-column prop="entityName" label="模型" show-overflow-tooltip>
          <template #default="{ row }">
            <img class="size-6 inline-block" :src="getModelLogo(row.entityName)" alt="模型logo" />
            {{ row.entityName }}
          </template>
        </el-table-column>
        <el-table-column prop="groupName" label="平台" show-overflow-tooltip />
        <el-table-column prop="inputTokens" label="输入Tokens数" sortable="custom">
          <template #default="{ row }">
            <span>{{ `${formatToken(row.inputTokens)}` }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="outputTokens" label="输出Tokens数" sortable="custom">
          <template #default="{ row }">
            <span>{{ `${formatToken(row.outputTokens)}` }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalTokens" label="调用总量Tokens数" sortable="custom">
          <template #default="{ row }">
            <span>{{ `${formatToken(row.totalTokens)}` }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="[10]"
        :page-size="pageSize"
        :total="total"
        layout="total, prev, pager, next, jumper"
        style="margin-top: 20px; justify-content: center"
      />
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.title {
  font-size: 16px;
  margin-bottom: 12px;
}
.model-invocation {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 28px;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-name {
    flex: 1;
    display: inline-flex;
    align-items: center;
    font-size: 18px;
    margin-bottom: 20px;
  }

  &-time {
    color: #646a73;
    font-size: 14px;
  }

  &-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }

  &-title {
    font-size: 16px;
    margin-bottom: 8px;
  }

  &-num {
    font-size: 28px;
    font-weight: 600;
    color: #4c5cec;
  }
}
</style>
