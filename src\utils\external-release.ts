import { OutwardType } from '@/api/external-release/type'
import webTypeIcon from '@/assets/settings/external-release/web-type-icon.png'
import wechatBotTypeIcon from '@/assets/settings/external-release/wechat-bot-type-icon.png'
import embedTypeIcon from '@/assets/settings/external-release/embed-type-icon.png'
import apiTypeIcon from '@/assets/settings/external-release/api-type-icon.png'
import ddBotTypeIcon from '@/assets/settings/external-release/dd-bot-type-icon.png'
import feishuBotTypeIcon from '@/assets/settings/external-release/feishu-bot-type-icon.png'

import webIcon from '@/assets/settings/external-release/web-icon.png'
import embedIcon from '@/assets/settings/external-release/embed-icon.png'
import wechatBotIcon from '@/assets/settings/external-release/wechat-bot-icon.png'
import apiIcon from '@/assets/settings/external-release/api-icon.png'
import ddBotIcon from '@/assets/settings/external-release/dd-bot-icon.png'
import feishuBotIcon from '@/assets/settings/external-release/feishu-type-icon.png'
/**
 * 发布类型列表
 */
export const publicationTypeList = [
  {
    icon: webTypeIcon,
    name: '网页对话',
    key: OutwardType.Web,
    publish: true,
  },
  {
    icon: wechatBotTypeIcon,
    name: '企微机器人',
    key: OutwardType.Wechat,
    publish: true,
  },
  {
    icon: feishuBotTypeIcon,
    name: '飞书机器人',
    key: OutwardType.Feishu,
    publish: true,
  },
  {
    icon: ddBotTypeIcon,
    name: '钉钉机器人',
    key: OutwardType.Dingtalk,
    publish: true,
  },
  {
    icon: embedTypeIcon,
    name: '嵌入悬浮框',
    key: OutwardType.Floating,
    publish: false,
  },
  {
    icon: apiTypeIcon,
    name: 'API',
    key: OutwardType.API,
    publish: false,
  },
]

/**
 * 根据发布类型显示对应的图标
 */
export function getOutwardIcon(code: OutwardType) {
  switch (code) {
    case OutwardType.API:
      return apiIcon
    case OutwardType.Floating:
      return embedIcon
    case OutwardType.Web:
      return webIcon
    case OutwardType.Wechat:
      return wechatBotIcon
    case OutwardType.Dingtalk:
      return ddBotIcon
    case OutwardType.Feishu:
      return feishuBotIcon
  }
}