<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue'
import { NLayout, NLayoutSider } from 'naive-ui'
import { ElButton, ElDialog, ElInput, ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import KnowledgeBaseSelector from './KnowledgeBaseSelector.vue'
import KnowledgeBaseSettingsPopover from './KnowledgeBaseSettingsPopover.vue'
import {
  postBizConversationBindKnowledge,
  postBizConversationUpdateConversation,
  updateBizGuestConversation,
} from '@/api'
import { HoverButton } from '@/components/common'
import { useChatStore } from '@/store'
import MarkdownEditor from '@/components/MarkdownEditor.vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'

// resolved 父组件里对应给initialModelConfig赋值吗
const props = withDefaults(defineProps<Props>(), {
  mobileMode: false,
  initialSystemMessage: '',
  initialKbList: () => [],
  initialModelConfig: () => ({
    temperature: 1.0,
    maxTokens: 2000,
    maxContextCount: 3,
    singleReplyLimitFlag: false,
  }),
  readonly: false,
})
const emit = defineEmits(['update:collapsed', 'close'])
const route = useRoute()
const isPublicChat = route.path.includes('/chat/public/')
const postConversationUpdateConversation = isPublicChat
  ? updateBizGuestConversation
  : postBizConversationUpdateConversation
const { isMobile } = useBasicLayout()

interface Props {
  mobileMode?: boolean
  initialSystemMessage?: string
  initialKbList?: KnowledgeBase.KnowledgeBaseVo[]
  initialModelConfig?: {
    temperature?: number
    maxTokens?: number
    maxContextCount?: number
    singleReplyLimitFlag?: boolean
  }
  readonly: boolean
}

const systemMessage = ref('')
const collapsed = ref(false)
const chatStore = useChatStore()

// 角色设定编辑相关
const showEditModal = ref(false)
const editMessage = ref('')
const isSubmitting = ref(false)

// 模型参数配置
const modelConfig = ref({
  temperature: 1.0,
  maxTokens: 2000,
  maxContextCount: 3,
  singleReplyLimitFlag: false,
})

// 当初始系统消息更新时，同步更新systemMessage
watch(
  () => props.initialSystemMessage,
  newValue => {
    // console.log('props.initialSystemMessage', newValue)
    if (newValue) {
      systemMessage.value = newValue
    }
  },
  { immediate: true },
)

// 当初始模型配置更新时，同步更新modelConfig
watch(
  () => props.initialModelConfig,
  newValue => {
    // console.log('props.initialModelConfig', newValue)
    if (newValue) {
      modelConfig.value = { ...newValue }
    }
  },
  { immediate: true },
)

// 知识库数据
const kbList = ref<KnowledgeBase.KnowledgeBaseVo[]>([])

watch(
  () => props.initialKbList,
  newValue => {
    // console.log('props.initialKbList', newValue)
    if (newValue) {
      kbList.value.push(...newValue)
    }
  },
  { immediate: true },
)

// 知识库选择器相关
const showKnowledgeBaseSelector = ref(false)
// 知识库设置相关
const showKnowledgeBaseSettings = ref(false)

// 检测是否是小尺寸屏幕（小于1600px宽度）
function isSmallScreen() {
  return window.innerWidth < 1600
}

function handleCollapse() {
  if (props.mobileMode) return
  collapsed.value = !collapsed.value
  emit('update:collapsed', collapsed.value)
}

function handleClose() {
  emit('close')
}

function openEditModal() {
  if (isPublicChat) return
  editMessage.value = systemMessage.value
  showEditModal.value = true
}

async function handleUpdateSystemMessage() {
  if (!chatStore.active) return

  try {
    isSubmitting.value = true
    await postConversationUpdateConversation({
      id: chatStore.active,
      // resolved systemMessage为null的情况，在network里看不到这个字段，是哪里过滤了呢
      systemMessage: editMessage.value,
    })
    // resolved 更新chatstore里对应的systemMessage
    // 如果要免去这一步去统一的话，可能要在chat页fetchConversationDetail()处理chatstore
    chatStore.updateHistory(chatStore.active, { systemMessage: editMessage.value })
    systemMessage.value = editMessage.value
    showEditModal.value = false
    ElMessage.success('角色设定更新成功')
  } catch (error) {
    console.error('更新系统角色设定失败:', error)
  } finally {
    isSubmitting.value = false
  }
}

function openKnowledgeBaseSelector() {
  if (isPublicChat || props.readonly) return
  showKnowledgeBaseSelector.value = true
}

function handleKnowledgeBaseSelected(kb: any) {
  if (kb.action === 'remove') {
    const index = kbList.value.findIndex(item => item.id?.toString() === kb.id?.toString())
    if (index !== -1) {
      removeKnowledgeBase(index)
    }
    return
  }
  console.log('选择了知识库:', kb)
  // 这里可以添加知识库到列表
  kbList.value.push(kb)
  // resolved 调用postBizConversationUpdateConversation接口更新对应的kbIdList
  updateKnowledgeBases()
}

function removeKnowledgeBase(index: number) {
  if (isPublicChat || props.readonly) return
  kbList.value.splice(index, 1)
  updateKnowledgeBases()
}

function updateKnowledgeBases() {
  if (chatStore.active) {
    const kbIds = kbList.value.map(item => item.id)
    postBizConversationBindKnowledge({
      conversationId: chatStore.active,
      kbIdList: kbIds,
    })
      .then(() => {
        ElMessage.success('知识库更新成功')
      })
      .catch(error => {
        console.error('知识库更新失败:', error)
      })
  }
}

async function updateModelConfig() {
  if (isPublicChat || !chatStore.active) return

  try {
    await postConversationUpdateConversation({
      id: chatStore.active,
      temperature: modelConfig.value.temperature,
      maxTokens: modelConfig.value.maxTokens,
      maxContextCount: modelConfig.value.maxContextCount,
      singleReplyLimitFlag: modelConfig.value.singleReplyLimitFlag,
    })
    // resolved 更新chatstore里对应的模型配置
    const updatedConfig = {
      temperature: modelConfig.value.temperature,
      maxTokens: modelConfig.value.maxTokens,
      maxContextCount: modelConfig.value.maxContextCount,
      singleReplyLimitFlag: modelConfig.value.singleReplyLimitFlag,
    }
    chatStore.updateHistory(chatStore.active, updatedConfig)
    ElMessage.success('模型参数更新成功')
  } catch (error) {
    console.error('更新模型参数失败:', error)
  }
}

watch(
  () => chatStore.active,
  () => {
    // 可以在会话变更时做一些处理
  },
  { immediate: true },
)

// 监听窗口大小变化
let resizeHandler: (() => void) | null = null

onMounted(() => {
  // 初始检测屏幕尺寸
  if (!props.mobileMode && isSmallScreen() && !collapsed.value) {
    collapsed.value = true
    emit('update:collapsed', true)
  }

  // 添加窗口大小变化监听
  resizeHandler = () => {
    if (!props.mobileMode) {
      const shouldCollapse = isSmallScreen()
      if (shouldCollapse !== collapsed.value) {
        collapsed.value = shouldCollapse
        emit('update:collapsed', shouldCollapse)
      }
    }
  }

  window.addEventListener('resize', resizeHandler)
})

onUnmounted(() => {
  // 移除事件监听器
  if (resizeHandler) {
    window.removeEventListener('resize', resizeHandler)
  }
})
</script>

<template>
  <div
    class="right-sider-container"
    :class="{
      'right-sider-collapsed': collapsed,
      'mobile-mode': mobileMode,
      'public-chat': isPublicChat,
    }"
  >
    <!-- 展开/收起按钮 -->
    <div v-if="!mobileMode" class="collapse-btn" :class="{ collapsed }" @click="handleCollapse" />

    <NLayout has-sider>
      <NLayoutSider
        bordered
        collapse-mode="width"
        :collapsed="collapsed"
        :collapsed-width="0"
        :width="mobileMode ? '100%' : 360"
      >
        <div class="h-full flex flex-col" :class="isMobile ? '' : 'w-[360px]'">
          <!-- 移动端头部 -->
          <div
            v-if="mobileMode"
            class="flex items-center justify-between p-4 border-b dark:border-neutral-800"
          >
            <span class="text-[16px] font-bold">辅助信息</span>
            <HoverButton @click="handleClose">
              <span class="text-xl text-[#4f555e] dark:text-white">
                <!-- 存疑 -->
                <i class="iconfont icon-guanbi" />
              </span>
            </HoverButton>
          </div>

          <!-- 角色设定部分 -->
          <div class="h-[190px] overflow-hidden dark:border-neutral-800">
            <div class="p-4 h-full flex flex-col">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <span class="text-[14px] font-bold">角色设定</span>
                </div>
                <!-- resolved 点击弹出对话框内含MarkdownEditor，点击确定提交接口postBizConversationUpdateConversation更新相关 -->
                <div class="flex items-center space-x-2" v-if="!readonly">
                  <i
                    style="font-size: 1.125rem"
                    class="iconfont iconfont-hover-c icon-bianjibi text-lg"
                    :class="
                      isPublicChat
                        ? 'text-gray-400 !cursor-not-allowed'
                        : 'cursor-pointer hover:text-primary'
                    "
                    @click="openEditModal"
                  />
                </div>
              </div>

              <div class="flex-1">
                <!-- {{ systemMessage || '请输入角色提示词' }} -->
                <!-- resolved 改成elementplus -->
                <ElInput
                  v-model="systemMessage"
                  readonly
                  type="textarea"
                  placeholder="请输入角色提示词..."
                  class="h-full"
                  :class="{ 'opacity-50': isPublicChat }"
                  :rows="5"
                  :style="{ borderRadius: '8px' }"
                />
              </div>
            </div>
          </div>
          <!-- 模型参数部分 -->
          <div class="dark:border-neutral-800">
            <div class="p-4 h-full flex flex-col">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <span class="text-[14px] font-bold">模型参数</span>
                </div>
              </div>
              <div class="flex-1">
                <div class="h-[200px] overflow-auto pr-1">
                  <!-- resolved 参考src\views\agent\my-agent\components\ModelSettings.vue，实现随机性、携带上下文轮数、开启单次回复限制、最大回复长度， 调用postBizConversationUpdateConversation接口更新，初始数据从父组件传入 -->
                  <div
                    class="model-settings-form pt-[10px] pr-[1px]"
                    :class="{ 'opacity-50': isPublicChat }"
                  >
                    <!-- modelConfig-{{ modelConfig }} -->
                    <!-- 随机性 -->
                    <div class="form-item">
                      <div class="form-left">
                        <div class="form-label">
                          <span>随机性</span>
                          <el-tooltip>
                            <template #content>
                              <div class="tooltip-content">
                                <p>
                                  数值越大，回答越有创意和想象力；数值越小，回答越严谨。默认值1.0。
                                </p>
                                <p>场景建议：</p>
                                <p>代码生成/数学解题：0.0</p>
                                <p>数据抽取/分析：1.0</p>
                                <p>通用对话：1.3</p>
                                <p>翻译：1.3</p>
                                <p>创意类写作/诗歌创作：1.5</p>
                              </div>
                            </template>
                            <i class="iconfont icon-tishi cursor-pointer ml-2" />
                          </el-tooltip>
                        </div>
                      </div>
                      <div class="form-right">
                        <el-slider
                          v-model="modelConfig.temperature"
                          :min="0"
                          :max="2"
                          :step="0.1"
                          :disabled="isPublicChat || readonly"
                          show-input
                          @change="updateModelConfig"
                        />
                      </div>
                    </div>
                    <!-- 携带上下文轮数 -->
                    <div class="form-item">
                      <div class="form-left">
                        <div class="form-label">
                          <span>携带上下文轮数</span>
                          <el-tooltip
                            content="设置带入模型上下文的对话历史轮数。轮数越多，多轮对话的相关性越高，但消耗的 Token 也越多。"
                          >
                            <i class="iconfont icon-tishi cursor-pointer ml-2" />
                          </el-tooltip>
                        </div>
                      </div>
                      <div class="form-right">
                        <el-slider
                          v-model="modelConfig.maxContextCount"
                          :min="1"
                          :max="20"
                          :step="1"
                          :disabled="isPublicChat || readonly"
                          show-input
                          @change="updateModelConfig"
                        />
                      </div>
                    </div>
                    <!-- 开启单次回复限制 -->
                    <div class="form-item">
                      <div class="form-left">
                        <div class="form-label">
                          <span>开启单次回复限制</span>
                        </div>
                      </div>
                      <div class="form-right flex justify-end">
                        <el-switch
                          v-model="modelConfig.singleReplyLimitFlag"
                          :disabled="isPublicChat || readonly"
                          @change="updateModelConfig"
                        />
                      </div>
                    </div>
                    <!-- 最大回复长度 -->
                    <div v-if="modelConfig.singleReplyLimitFlag" class="form-item">
                      <div class="form-left">
                        <div class="form-label">
                          <span>最大回复长度</span>
                          <el-tooltip
                            content="控制模型输出的 Tokens 长度上限。通常 100 Tokens 约等于 150 个中文汉字。"
                          >
                            <i class="iconfont icon-tishi cursor-pointer ml-2" />
                          </el-tooltip>
                        </div>
                      </div>
                      <div class="form-right">
                        <el-slider
                          v-model="modelConfig.maxTokens"
                          :min="0"
                          :max="8000"
                          :step="100"
                          :disabled="isPublicChat || readonly"
                          show-input
                          @change="updateModelConfig"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 知识库部分 -->
          <!-- <div class="h-[300px]"> -->
          <div class="flex-1 flex flex-col min-h-0">
            <div class="p-4 flex flex-col flex-grow min-h-0">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <span class="font-bold text-[14px]">知识库</span>
                </div>
                <div class="flex items-center space-x-2">
                  <!-- resolved 创建一个弹框组件el-popover，点击弹出 -->
                  <KnowledgeBaseSettingsPopover v-model:visible="showKnowledgeBaseSettings">
                    <i
                      style="font-size: 1.05rem"
                      class="iconfont iconfont-hover-c icon-zhishikushezhi text-lg"
                      :class="
                        isPublicChat || readonly
                          ? 'text-gray-400 !cursor-not-allowed'
                          : 'cursor-pointer hover:text-primary'
                      "
                      @click="isPublicChat || readonly ? null : (showKnowledgeBaseSettings = true)"
                    />
                  </KnowledgeBaseSettingsPopover>
                  <i
                    style="font-size: 1.125rem; margin-top: -2px"
                    class="iconfont iconfont-hover-c icon-xinzengzhishiku text-lg"
                    :class="
                      isPublicChat || readonly
                        ? 'text-gray-400 !cursor-not-allowed'
                        : 'cursor-pointer hover:text-primary'
                    "
                    @click="openKnowledgeBaseSelector"
                  />
                </div>
              </div>
              <div class="space-y-4">
                <!-- resolved 遍历数量很多时候超出高度，要求可以滚动 -->
                <!-- resolved 设置最大高度以支持滚动，使用calc计算剩余空间 -->
                <div
                  v-if="kbList && kbList.length"
                  class="space-y-2 max-h-[calc(100vh-620px)] overflow-y-auto pr-1"
                >
                  <div
                    v-for="(kb, index) in kbList"
                    :key="kb.id"
                    class="kb-item flex items-center justify-between p-2 rounded-lg border g-bg-hover"
                    :class="
                      isPublicChat || readonly ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    "
                  >
                    <div class="flex items-center overflow-hidden">
                      <!-- resolved SvgIcon改成src\assets\chat\knowledge-base-icon-0.svg -->
                      <img
                        v-if="kb.enabled"
                        class="text-lg mr-2 flex-shrink-0 w-6 h-6"
                        src="@/assets/chat/knowledge-base-icon-0.svg"
                        alt="知识库图标"
                      />
                      <el-tooltip v-else content="未启用" placement="top">
                        <div class="relative">
                          <img
                            class="text-lg mr-2 flex-shrink-0 w-6 h-6"
                            src="@/assets/chat/knowledge-base-icon-0.svg"
                            alt="知识库图标"
                          />
                          <img
                            class="absolute top-0 right-2 w-3 h-3"
                            src="@/assets/chat/error.svg"
                            alt="错误图标"
                          />
                        </div>
                      </el-tooltip>
                      <span class="text-[14px] truncate">{{ kb.name }}</span>
                    </div>
                    <i
                      style="font-size: 1.5rem"
                      class="iconfont icon-shanchu text-lg flex-shrink-0 delete-icon"
                      :class="
                        isPublicChat || readonly
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'cursor-pointer hover:text-red-500'
                      "
                      @click.stop="removeKnowledgeBase(index)"
                    />
                  </div>
                </div>
                <el-empty v-else description="暂无数据" />
              </div>
            </div>
          </div>
        </div>
      </NLayoutSider>
    </NLayout>

    <!-- 编辑角色设定对话框 -->
    <!-- resolved 换成elementplus组件 -->
    <ElDialog
      v-model="showEditModal"
      draggable
      :width="isMobile ? '90%' : '1000px'"
      :close-on-click-modal="false"
      style="padding-top: 0"
    >
      <div class="text-lg font-medium mb-4 text-[#30343A]">编辑角色设定</div>
      <div class="h-[400px] mb-4 border rounded-[8px] p-[8px]">
        <MarkdownEditor v-model:model-value="editMessage" />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="showEditModal = false">取消</ElButton>
          <ElButton type="primary" :loading="isSubmitting" @click="handleUpdateSystemMessage"
            >确定</ElButton
          >
        </div>
      </template>
    </ElDialog>

    <!-- 知识库选择弹窗 -->
    <!-- todo 子组件里需要根已选择的kbList去判断item的状态 -->
    <KnowledgeBaseSelector
      v-model:visible="showKnowledgeBaseSelector"
      :initial-kb-list="kbList"
      @selected="handleKnowledgeBaseSelected"
    />
  </div>
</template>

<style scoped lang="less">
.right-sider-container {
  position: relative;
  height: 100%;
  transition: all 0.3s ease;
  border-left: 1px solid #e5e7eb;

  &.mobile-mode {
    border-left: none;
    width: 100%;
  }
}

/* 添加右侧栏展开/收起时的样式 */
:global(.right-sider-expanded) {
  padding-right: 360px;
}

:global(.right-sider-collapsed) {
  padding-right: 0;
}

.collapse-btn {
  position: absolute;
  left: -44px;
  top: 25px;
  width: 44px;
  height: 40px;
  border-right: none;
  border-radius: 4px 0 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  background: url('@/assets/right-sider-btn-0.png') no-repeat center center / 100% 100%;
  &:hover {
    background: url('@/assets/right-sider-btn-1.png') no-repeat center center / 100% 100%;
  }

  &.collapsed {
    background: url('@/assets/right-sider-btn-0-re.png') no-repeat center center / 100% 100%;
    &:hover {
      background: url('@/assets/right-sider-btn-1-re.png') no-repeat center center / 100% 100%;
    }
  }
}

.collapse-btn:hover {
  background-color: #f3f4f6;
}

/* 添加右侧栏收起时的样式 */
.right-sider-collapsed .collapse-btn {
  border-right: 1px solid #e5e7eb;
  border-radius: 4px 0 0 4px;
}

.dark .collapse-btn {
  background-color: #1f1f1f;
  border-color: #2d2d2d;
}

.dark .collapse-btn:hover {
  background-color: #2d2d2d;
}

.dark .text-neutral-600 {
  color: rgba(156, 163, 175, 1);
}

:deep(.n-input) {
  background-color: transparent;
  height: 100%;
}

:deep(.n-layout) {
  background-color: transparent;
}

:deep(.n-layout-sider) {
  background-color: transparent;
}

:deep(.n-input__textarea-el) {
  background-color: transparent !important;
  height: 100% !important;
  resize: none !important;
}

.text-primary {
  color: #4b5563;
}

.dark .text-primary {
  color: #9ca3af;
}

:deep(.n-layout-sider-border) {
  border-left: 1px solid var(--n-border-color);
}

.delete-icon {
  opacity: 0;
  transition: opacity 0.3s;
}
.kb-item:hover .delete-icon {
  opacity: 1;
}

/* 公开聊天模式下禁用hover效果 */
.right-sider-container.public-chat .kb-item:hover .delete-icon {
  opacity: 0;
}

.right-sider-container.public-chat .g-bg-hover:hover {
  background-color: transparent !important;
}

:deep(.n-layout-sider-scroll-container) {
  overflow: hidden !important;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0px;
}

.model-settings-form {
  :deep(.el-slider__input) {
    width: 70px;
    .el-input-number__decrease {
      display: none;
    }
    .el-input-number__increase {
      display: none;
    }
    .el-input__wrapper {
      padding-left: 0;
      padding-right: 0;
    }
  }
  :deep(.el-slider__runway) {
    background-color: #e4e7ed;
    height: 4px;
  }
  :deep(.el-slider__bar) {
    background-color: #9da0a6;
    height: 4px;
  }
  :deep(.el-slider__button) {
    width: 10px;
    height: 10px;
    background-color: #ffffff;
    border: solid 2px #9da0a6;
  }
  :deep(.el-slider__button-wrapper) {
    top: -9px;
    height: 16px;
  }

  .form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .form-left {
      .form-label {
        font-size: 14px;
        color: #30343a;
        font-weight: 500;
        display: flex;
        align-items: center;
      }
      .form-instruction {
        font-size: 12px;
        color: #969ba4;
        line-height: 17px;
      }
    }
    .form-right {
      width: 180px;
      :deep(.el-input) {
        min-height: 32px !important;
        .el-input__wrapper {
          min-height: 32px !important;
          border-radius: 8px;
        }
        .el-input__inner {
          min-height: 30px !important;
        }
      }
    }
  }
  .form-item + .form-item {
    margin-top: 20px;
  }
}
</style>
