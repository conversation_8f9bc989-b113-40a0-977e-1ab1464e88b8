import { OutwardType } from "../external-release/type"

export enum StatType {
  Model = "MODEL",
  Outward = "OUTWARD",
  User = "USER",
}
// 时间序列查询参数
export interface TokenUsageTimeSeriesQueryBo {
  startTime: string // 开始时间
  endTime: string // 结束时间
  granularity: 'HOUR' | 'DAY' // 时间粒度：HOUR/DAY
  modelIds?: (string | number)[] // 模型列表（可选）
  userIds?: (string | number)[] // 用户列表（可选）
  deptIds?: (string | number)[] // 部门列表（可选）
  outwardIds?: (string | number)[] // 应用列表（可选）
  /**
   * 统计类型：MODEL/USER/OUTWARD
   * MODEL：默认，不额外过滤
   * USER：过滤掉 create_dept 为 -1 或 NULL 的数据
   * OUTWARD：过滤掉 outward 为空的数据
   */
  statType?: StatType
}

// 时间序列数据点
export interface TimeSeriesDataPoint {
  time: string // 时间点
  totalTokens: number // 总token数
  inputTokens: number // 输入token数
  outputTokens: number // 输出token数
  chatCount: number // 对话次数
  totalCost: number // 总成本
}

// 汇总统计
export interface Summary {
  totalTokens: number // 总token数
  inputTokens: number // 输入token数
  outputTokens: number // 输出token数
  chatCount: number // 对话次数
  totalCost: number // 总成本
}

// Token使用量时间序列结果
export interface TokenUsageTimeSeriesVo {
  dataPoints: TimeSeriesDataPoint[] // 时间序列数据点列表
  summary: Summary // 汇总统计
}

export type AggregateType = 'MODEL' | 'USER' | 'OUTWARD'

// 聚合查询参数
export interface TokenUsageAggregateQueryBo {
  startTime: string // 开始时间
  endTime: string // 结束时间
  aggregateType: AggregateType // 分组类型：MODEL/USER/OUTWARD
  modelIds?: number[] // 模型列表（可选）
  userIds?: number[] // 用户列表（可选）
  outwardIds?: number[] // 应用列表（可选）
}

// 聚合数据点
export interface AggregateDataPoint {
  entityId: string // 实体ID（模型ID/用户ID/应用ID）
  entityName: string // 实体名称
  groupName: string // 分组名称（平台名/部门名/助手名）
  totalTokens: number // 总token数
  inputTokens: number // 输入token数
  outputTokens: number // 输出token数
  totalCost: number // 总成本
  platformIconUrl: string | null
  type: string | OutwardType | null
  /**
   * 助手图标背景(仅当按 OUTWARD 分组时返回)
   */
  backgroundColor?: string;
  /**
   * 对话次数
   */
  chatCount?: number;
  /**
   * 助手图标(仅当按 OUTWARD 分组时返回)
   */
  emoji?: string;
}

// Token使用量聚合结果
export interface TokenUsageAggregateVo {
  dataPoints: AggregateDataPoint[] // 聚合数据点列表
}

// 聚合接口响应
export interface RTokenUsageAggregateVo {
  code: number
  msg: string
  data: TokenUsageAggregateVo
}
// 聚合分页查询参数
export interface TokenUsageAggregatePageQueryBo {
  /**
   * 关联助手列表（可选，仅在按 OUTWARD 分组时生效）
   */
  agentIds?: (number | string)[];
  /**
    * 分组类型：MODEL/USER/OUTWARD
    */
  aggregateType: AggregateType;
  /**
   * 部门列表（可选）
   */
  deptIds?: (number | string)[];
  /**
   * 结束时间
   */
  endTime: string;
  /**
   * 模型列表（可选）
   */
  modelIds?: (number | string)[];
  /**
   * 模型名称列表（可选，仅在按 MODEL 分组时生效）
   */
  modelNames?: string[];
  /**
   * 成员姓名列表（可选，仅在按 USER 分组时生效）
   */
  nickNames?: string[];
  /**
   * MyBatis 使用的偏移量
   */
  offset?: number;
  /**
   * 应用列表（可选）
   */
  outwardIds?: (number | string)[];
  /**
   * 应用名称列表（可选，仅在按 OUTWARD 分组时生效）
   */
  outwardNames?: string[];
  /**
   * 应用类型列表（可选，仅在按 OUTWARD 分组时生效）。如：EASE_IDEA、WEB、WECHAT、DINGTALK、FEISHU、FLOATING、API
   */
  outwardTypes?: string[];
  /**
   * 当前页码，默认 1
   */
  pageNum?: number;
  /**
   * 每页大小，默认 10
   */
  pageSize?: number;
  /**
   * 模型平台列表（可选，仅在按 MODEL 分组时生效）
   */
  platformIds?: number[];
  /**
   * 排序字段：totalTokens/inputTokens/outputTokens
   */
  sortBy?: string;
  /**
   * 排序方式：ASC/DESC
   */
  sortOrder?: string;
  /**
   * 开始时间
   */
  startTime: string;
  /**
   * 用户列表（可选）
   */
  userIds?: (number | string)[];
}

/**
 * SysUserVo，用户信息视图对象 sys_user
 */
export interface SysUserVo {
  /**
   * 激活状态（1未激活 0已激活）
   */
  activeState?: string;
  /**
   * 头像地址
   */
  avatar?: number;
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 部门ID
   */
  deptId?: number;
  /**
   * 部门名
   */
  deptName?: string;
  /**
   * 用户邮箱
   */
  email?: string;
  /**
   * 加入时间(激活时间)
   */
  joinDate?: Date;
  /**
   * 最后登录时间
   */
  loginDate?: Date;
  /**
   * 最后登录IP
   */
  loginIp?: string;
  /**
   * 用户昵称
   */
  nickName?: string;
  /**
   * 手机号码
   */
  phonenumber?: string;
  /**
   * 岗位组
   */
  postIds?: number[];
  /**
   * 备注
   */
  remark?: string;
  /**
   * 数据权限 当前角色ID
   */
  roleId?: number;
  /**
   * 角色组
   */
  roleIds?: number[];
  /**
   * 角色对象
   */
  roles?: SysRoleVo[];
  /**
   * 是否与手机号相同
   */
  sameNickNameWithPhonenumber?: boolean;
  /**
   * 用户性别（0男 1女 2未知）
   */
  sex?: string;
  /**
   * 帐号状态（0正常 1停用）
   */
  status?: string;
  /**
   * 租户ID
   */
  tenantId?: string;
  /**
   * 租户name
   */
  tenantName?: string;
  /**
   * 用户ID
   */
  userId?: number;
  /**
   * 用户账号
   */
  userName?: string;
  /**
   * 用户类型（sys_user系统用户）
   */
  userType?: string;
  [property: string]: any;
}

/**
 * SysRoleVo，角色信息视图对象 sys_role
 */
export interface SysRoleVo {
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）
   */
  dataScope?: string;
  /**
   * 部门树选择项是否关联显示
   */
  deptCheckStrictly?: boolean;
  /**
   * 用户是否存在此角色标识 默认不存在
   */
  flag?: boolean;
  /**
   * 菜单树选择项是否关联显示
   */
  menuCheckStrictly?: boolean;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 角色ID
   */
  roleId?: number;
  /**
   * 角色权限字符串
   */
  roleKey?: string;
  /**
   * 角色名称
   */
  roleName?: string;
  /**
   * 显示顺序
   */
  roleSort?: number;
  /**
   * 角色状态（0正常 1停用）
   */
  status?: string;
  superAdmin?: boolean;
  [property: string]: any;
}


/**
 * SysDeptVo，部门视图对象 sys_dept
 */
export interface SysDeptVo {
  /**
   * 祖级列表
   */
  ancestors?: string;
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 部门类别编码
   */
  deptCategory?: string;
  /**
   * 部门id
   */
  deptId?: number;
  /**
   * 部门名称
   */
  deptName?: string;
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 负责人ID
   */
  leader?: number;
  /**
   * 负责人
   */
  leaderName?: string;
  /**
   * 显示顺序
   */
  orderNum?: number;
  /**
   * 父部门id
   */
  parentId?: number;
  /**
   * 父部门名称
   */
  parentName?: string;
  /**
   * 联系电话
   */
  phone?: string;
  /**
   * 部门状态（0正常 1停用）
   */
  status?: string;
  [property: string]: any;
}

