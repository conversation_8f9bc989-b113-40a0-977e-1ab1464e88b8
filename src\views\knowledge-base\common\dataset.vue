<script setup lang="ts">
import DatasetTable from './modules/DatasetTable.vue'
import AddFile from './modules/AddFile.vue'
import DocDetail from './modules/DocDetail.vue'
import { kbIdKey } from '@/utils/constants'
import { useKnowledgeFileUpload } from '@/hooks/useKnowledgeFileUpload'
import { useStorage } from '@vueuse/core'

const route = useRoute()
const router = useRouter()
const kbId = inject(kbIdKey) as string
type ModeType = 'table' | 'add' | 'detail'

// 初始化时从URL读取mode参数
const mode = ref<ModeType>((route.query.mode as ModeType) || 'table')

/**
 * 文档信息
 */
const docInfo = useStorage<KnowledgeBase.KnowledgeBaseItemVo | {}>('doc-info', {})
const { clearFileList } = useKnowledgeFileUpload()

function changeMode(m: ModeType, payload?: KnowledgeBase.KnowledgeBaseItemVo) {
  mode.value = m
  // 更新URL参数
  router.replace({
    query: {
      ...route.query,
      mode: m,
    },
  })
  if (m === 'add') {
    clearFileList()
  }
  if (payload) {
    docInfo.value = payload
  }
}
</script>

<template>
  <div class="p-[20px] flex flex-col h-full">
    <!-- <component :is="comp" :kbId="kbId" :docInfo="docInfo" @changeMode="changeMode" /> -->
    <DatasetTable v-if="mode === 'table'" :kb-id="kbId" @change-mode="changeMode" />
    <AddFile v-if="mode === 'add'" :kb-id="kbId" @change-mode="changeMode" />
    <DocDetail
      v-if="mode === 'detail'"
      :kb-id="kbId"
      :doc-info="docInfo"
      @change-mode="changeMode"
    />
  </div>
</template>

<style scoped lang="less"></style>
