{"compilerOptions": {"baseUrl": ".", "module": "ESNext", "target": "ESNext", "lib": ["DOM", "ESNext"], "strict": true, "noImplicitAny": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "moduleResolution": "node", "resolveJsonModule": true, "noUnusedLocals": false, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "node", "naive-ui/volar", "vitest/globals", "@testing-library/jest-dom"]}, "exclude": ["node_modules", "dist", "service"]}