<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { getBizAiModelAvailableModels } from '@/api'
import { getDeptUserList } from '@/api/usage'
import { queryOutward } from '@/api/external-release'
import type { OutwardVo } from '@/api/external-release/type'
import { AggregateType } from '@/api/usage/types'
import dayjs from 'dayjs'
import { useDeptList } from '../hooks/useDeptList'
// import { getModelLogo } from '@/utils/models'

// 定义类型
type GranularityType = 'DAY' | 'HOUR'

// interface ModelTypeOptionItem {
//   label: string
//   value: 'text' | 'image' | 'embedding' | 'rerank'
// }

// 定义 Props
interface Props {
  currentTab: AggregateType
}

const props = defineProps<Props>()

interface OptionItem {
  label: string
  value: string
}

// 内部数据状态
const modelType = ref<'text' | 'image' | 'embedding' | 'rerank'>('text') // 默认选择大语言模型
const modelOptions = ref<OptionItem[]>([])
const userOptions = ref<OptionItem[]>([])
const outwardOptions = ref<OptionItem[]>([])

// 模型类型选项列表
// const modelTypeOptions: ModelTypeOptionItem[] = [
//   {
//     label: '大语言模型',
//     value: 'text',
//   },
//   {
//     label: '图像模型',
//     value: 'image',
//   },
//   {
//     label: '嵌入模型',
//     value: 'embedding',
//   },
//   {
//     label: '重排序模型',
//     value: 'rerank',
//   },
// ]

// 获取模型列表
const fetchModelOptions = async () => {
  try {
    const res = await getBizAiModelAvailableModels({ type: modelType.value })
    if (res.code === 200) {
      modelOptions.value = [
        // {
        //   value: 'all',
        //   label: '全部',
        // },
        ...res.data.map((model: any) => ({
          ...model,
          value: model.id,
          label: model.displayName || model.name,
        })),
      ]
    }
  } catch (error) {
    console.error('获取模型列表失败:', error)
  }
}

// 处理模型类型变化
// const handleModelTypeChange = async (value: 'text' | 'image' | 'embedding' | 'rerank') => {
//   // 重新获取模型列表
//   await fetchModelOptions()
//   // 重置选中的模型为 "全部"
//   modelSelectedModel.value = 'all'
// }

// 获取应用列表
const fetchOutwardOptions = async () => {
  try {
    // 设置一个较大的pageSize以尽量减少请求次数
    const pageSize = 1000
    let pageNum = 1
    let allOutwards: OutwardVo[] = []
    let hasMore = true

    // 循环获取所有数据
    while (hasMore) {
      const res = await queryOutward({ pageNum, pageSize })
      if (res.code === 200) {
        allOutwards = [...allOutwards, ...res.rows]
        // 如果当前页的数据少于pageSize，说明已经到最后一页了
        hasMore = res.total > pageSize
        pageNum++
      } else {
        console.error('获取应用列表失败:', res)
        hasMore = false
      }
    }

    // 将获取到的数据转换为下拉框需要的格式
    outwardOptions.value = [
      ...allOutwards.map((outward: any) => ({
        value: outward.id,
        label: outward.name,
      })),
    ]
  } catch (error) {
    console.error('获取应用列表失败:', error)
  }
}

const { deptOptions, getDeptList } = useDeptList()

// 获取部门列表
const fetchDeptOptions = () => {
  userOptions.value = []
  getDeptList()
}

// 根据部门ID获取用户列表
const fetchUserOptions = async (deptId: string | number) => {
  if (!deptId) {
    userOptions.value = []
    return
  }

  try {
    const res = await getDeptUserList(deptId)
    if (res.code === 200) {
      userOptions.value = res.data.map((user: any) => ({
        value: user.userId,
        label: user.nickName || user.userName,
      }))
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 使用 defineModel 替代 defineProps + emit 的双向绑定写法
const modelDateRange = defineModel<[Date, Date]>('dateRange', { required: true })
const modelGranularity = defineModel<GranularityType>('granularity', { required: true })
const modelSelectedModel = defineModel<string | number>('selectedModel', { required: true })
const modelSelectedDept = defineModel<string | number>('selectedDept', {
  required: true,
})
const modelSelectedUser = defineModel<string | number>('selectedUser', {
  required: true,
})
const modelSelectedOutward = defineModel<string | number>('selectedOutward', {
  required: true,
})

const emit = defineEmits<{
  (event: 'change', field?: string): void
}>()

// 组件挂载时获取初始数据
onMounted(() => {
  // 根据当前 tab 获取相应的数据
  if (props.currentTab === 'MODEL') {
    fetchModelOptions()
  } else if (props.currentTab === 'USER') {
    fetchDeptOptions()
  } else if (props.currentTab === 'OUTWARD') {
    fetchOutwardOptions()
  }
})

// 监听 currentTab 的变化，当变化时触发重置逻辑和数据获取
watch(
  () => props.currentTab,
  (newTab, oldTab) => {
    // 当 tab 切换时，重置隐藏的值
    if (oldTab !== newTab) {
      // 根据新 tab 获取相应的数据
      if (newTab === 'MODEL') {
        fetchModelOptions()
      } else if (newTab === 'USER') {
        fetchDeptOptions()
      } else if (newTab === 'OUTWARD') {
        fetchOutwardOptions()
      }
    }
  },
)

// 事件处理函数
const handleDateChange = (value: [Date, Date] | null) => {
  if (value) {
    const startDate = dayjs(value[0])
    let endDate = dayjs(value[1])
    const diffDays = endDate.diff(startDate, 'day')

    if (diffDays > 29) {
      ElMessage.warning('最多只能选择30天内的日期范围')
      endDate = startDate.add(29, 'day')
    }
    modelDateRange.value = [startDate.toDate(), endDate.toDate()]
    emit('change')
  }
}

// 判断日期是否应该被禁用（禁止选择今天以后的日期，以及超出30天范围的日期）
const isDateDisabled = (date: Date) => {
  // 获取今天的日期，并将时间设置为 00:00:00
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // 将传入的日期时间也设置为 00:00:00 进行比较
  const compareDate = new Date(date)
  compareDate.setHours(0, 0, 0, 0)

  // 如果传入的日期大于今天的日期，则禁用
  return compareDate > today
}

const handleGranularityChange = () => {
  emit('change', 'granularity')
}

const handleModelChange = (value: string) => {
  emit('change', 'modelIds')
}

const handleDeptChange = (value: string) => {
  modelSelectedUser.value = ''
  // 获取用户列表
  fetchUserOptions(value)
  emit('change', 'deptIds')
}

const handleUserChange = (value: string) => {
  emit('change', 'userIds')
}

const handleOutwardChange = (value: string) => {
  emit('change', 'outwardIds')
}
</script>

<template>
  <div class="usage-chart-card-query">
    <div style="white-space: nowrap; padding-right: 20px">大语言模型</div>

    <!-- <el-select v-model="modelType" disabled style="padding-right: 20px; width: 160px">
      <el-option
        v-for="item in modelTypeOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select> -->
    <el-space wrap>
      <el-date-picker
        v-model="modelDateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        style="width: 300px"
        size="large"
        :clearable="false"
        :disabled-date="isDateDisabled"
        @change="handleDateChange"
      />
      <el-radio-group
        fill="#4865E8"
        text-color="#FFFFFF"
        class="custom-radio-group"
        v-model="modelGranularity"
        @change="handleGranularityChange"
      >
        <el-radio-button value="DAY">天</el-radio-button>
        <el-radio-button value="HOUR">小时</el-radio-button>
      </el-radio-group>
      <!-- <el-select
        v-if="currentTab === 'MODEL'"
        v-model="modelSelectedModel"
        placeholder="请选择模型"
        style="width: 360px"
        filterable
        @change="handleModelChange"
      >
        <el-option
          v-for="item in modelOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
          <img class="model-logo" :src="getModelLogo(item.label, '')" alt="" />
          {{ item.label }}
        </el-option>
      </el-select> -->
      <Selector
        v-if="currentTab === 'MODEL'"
        v-model="modelSelectedModel"
        :opentionList="modelOptions"
        :width="360"
        class="model-selector"
        placeholder="全部"
        clearable
        @change="handleModelChange('modelId')"
      >
      </Selector>

      <el-tree-select
        v-if="currentTab === 'USER'"
        v-model="modelSelectedDept"
        :data="deptOptions"
        check-strictly
        placeholder="选择部门"
        style="width: 320px"
        :render-after-expand="false"
        default-expand-all
        clearable
        @change="handleDeptChange"
      >
      </el-tree-select>

      <el-select
        v-if="currentTab === 'USER'"
        v-model="modelSelectedUser"
        placeholder="选择人员"
        :no-data-text="modelSelectedDept ? '' : '请先选择部门'"
        style="width: 320px"
        clearable
        filterable
        @change="handleUserChange"
      >
        <el-option
          v-for="item in userOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-select
        v-if="currentTab === 'OUTWARD'"
        v-model="modelSelectedOutward"
        placeholder=" 选择应用"
        style="width: 320px"
        filterable
        clearable
        @change="handleOutwardChange"
      >
        <el-option
          v-for="item in outwardOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-space>
  </div>
</template>

<style lang="less" scoped>
.usage-chart-card-query {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 24px;
}

// .model-logo {
//   display: inline-block;
//   width: 20px;
//   height: 20px;
//   margin-right: 8px;
// }

.model-selector {
  height: 40px;
}

.custom-radio-group {
  background-color: #f5f7fb; /* 灰色背景 */
  padding: 4px;
  border-radius: 8px;

  :deep(.el-radio-button__inner) {
    border: 0;
    border-radius: 4px;
  }
}
</style>
