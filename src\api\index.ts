import { deleteReq, get, post } from '@/utils/request'
import { useSettingStore } from '@/store'
import { ConversationVo } from './external-release/type';

export function fetchChatAPI<T = any>(
  prompt: string,
  options?: { conversationId?: string; parentMessageId?: string },
  signal?: GenericAbortSignal,
) {
  return post<T>({
    url: '/chat',
    data: { prompt, options },
    signal,
  })
}

export function fetchChatConfig<T = any>() {
  return post<T>({
    url: '/config',
  })
}

export function fetchChatAPIProcess<T = any>(params: {
  prompt: string
  options?: { conversationId?: string; parentMessageId?: string }
  signal?: GenericAbortSignal
  onDownloadProgress?: (progressEvent: AxiosProgressEvent) => void
}) {
  const settingStore = useSettingStore()

  let data: Record<string, any> = {
    prompt: params.prompt,
    options: params.options,
  }

  data = {
    ...data,
    systemMessage: settingStore.systemMessage,
    temperature: settingStore.temperature,
    top_p: settingStore.top_p,
  }

  return post<T>({
    url: '/chat-process',
    data,
    signal: params.signal,
    onDownloadProgress: params.onDownloadProgress,
  })
}

// resolved 移除fetchSession函数，因为auth模块已废弃

export function fetchVerify<T>(token: string) {
  return post<T>({
    url: '/verify',
    data: { token },
  })
}

/** 会话 */
// 修改会话
export function postBizConversationUpdateConversation<T>(data: any) {
  return post<T>({
    url: '/biz/conversation/updateConversation',
    data,
  })
}

// 终止对话
export function postBizConversationCloseConversation<T>(data: any) {
  return post<T>({
    url: `/biz/conversation/close/${data.id}`,
    data,
  })
}

// 重试
export function postBizConversationRetry<T>(data: any) {
  return post<T>({
    url: '/biz/conversation/retry',
    data,
  })
}

// 删除会话
export function postBizConversationRemove<T>(data: Record<string, any>) {
  return post<T>({
    url: `/biz/conversation/remove?id=${data.id}`,
    data,
  })
}

// 提示词测试
export function postBizConversationPromptTest<T>(data: Record<string, any>) {
  return post<T>({
    url: '/biz/conversation/promptTest',
    data,
  })
}

// 创建对话
export function postBizConversationCreateConversation<T>(data: Record<string, any>) {
  return post<T>({
    url: '/biz/conversation/createConversation',
    data,
  })
}

// 对话
export function postBizConversationCompletions<T>(data: Record<string, any>) {
  return post<T>({
    url: '/biz/conversation/completions',
    data,
  })
}

// 清空对话上下文
export function postBizConversationClearContext<T>(data: Record<string, any>) {
  return post<T>({
    url: '/biz/conversation/clearContext',
    data,
  })
}

// 会话绑定知识库
export function postBizConversationBindKnowledge<T>(data: any) {
  return post<T>({
    url: '/biz/conversation/bind/knowledge',
    data,
  })
}

// 应用助手
export function postBizConversationApplyAgent<T>(data: Record<string, any>) {
  return post<T>({
    url: `/biz/conversation/applyAgent?id=${data.id}`,
  })
}

// 消息摘要
export function getBizConversationMessageSummary<T>(data: any) {
  return get<T>({
    url: '/biz/conversation/messageSummary',
    data,
  })
}

// 会话列表
export function getBizConversationList<T>(data: any) {
  return get<T>({
    url: '/biz/conversation/list',
    data,
  })
}

// 获取默认对话
export function getBizConversationGetDefaultConversation<T>() {
  return get<T>({
    url: '/biz/conversation/getDefaultConversation',
  })
}

// 会话详情
export function getBizConversationDetail<T>(data: any) {
  return get<T>({
    url: '/biz/conversation/detail',
    data,
  })
}

/** 对话 */
// 编辑对话
export function postBizChatUpdate<T>(data: Record<string, any>) {
  return post<T>({
    url: '/biz/chat/update',
    data,
  })
}

// 获取对话记录
export function getBizChatGetChatRecord<T>(data: {
  orderByColumn?: string
  isAsc?: string
  conversationId: string | number
  pageNum?: number
  pageSize?: number
}) {
  return get<T>({
    url: '/biz/chat/getChatRecord',
    data,
  })
}

// 删除对话
export function postBizChatRemove<T>(data: Record<string, any>) {
  return post<T>({
    url: `/biz/chat/remove?id=${data.id}`,
    data,
  })
}

/** 模型 */
// 保存默认模型配置
export function postBizAiModelSaveDefault<T>(data: Model.ModelDefault) {
  return post<T>({
    url: '/biz/aiModel/save/default',
    data,
  })
}

// // 更新
// export function postBizAiModelUpdate<T>(data: Record<string, any>) {
//   return post<T>({
//     url: '/biz/aiModel/update',
//     data,
//   })
// }

// 查看默认模型配置
export function getBizAiModelDefault() {
  return get<Model.ModelDefault>({
    url: '/biz/aiModel/get/default',
  })
}

// 启用/禁用
export function postBizAiModelEnable<T>(data: Record<string, any>) {
  return post<T>({
    url: `/biz/aiModel/enable?id=${data.id}`,
    data,
  })
}

// // 添加
// export function postBizAiModelAdd<T>(data: Record<string, any>) {
//   return post<T>({
//     url: '/biz/aiModel/add',
//     data,
//   })
// }

// // 列表
// export function getBizAiModelList<T>(data: any) {
//   return get<T>({
//     url: '/biz/aiModel/list',
//     data,
//   })
// }

// 获取模型平台下拉框
export function getBizAiModelGetPlatform<T>(data: any) {
  return get<T>({
    url: '/biz/aiModel/get/platform',
    data,
  })
}

// 获取模型对应的配置key
export function getBizAiModelGetConfigKey<T>(data: any) {
  return get<T>({
    url: '/biz/aiModel/get/config/key',
    data,
  })
}

// // 检查模型是否可用
// export function getBizAiModelCheckModelStatus<T>(data: any) {
//   return get<T>({
//     url: '/biz/aiModel/checkModelStatus',
//     data,
//   })
// }

export interface AIModelVo {
  /**
   * 模型请求地址
   */
  baseUrl?: string
  /**
   * 显示名称
   */
  displayName: string
  id: string
  /**
   * 输入token单价
   */
  inputTokenUnitPrice?: number
  /**
   * 是否启用 0-未启用 1-启用
   */
  isEnabled?: boolean
  /**
   * 最大输入token数
   */
  maxInputTokens?: number
  /**
   * 最大输出token数
   */
  maxOutputTokens?: number
  /**
   * 最大token数
   */
  maxTokens?: number
  /**
   * 多模态能力
   */
  multimodalFlag?: boolean
  /**
   * 模型名称
   */
  name: string
  /**
   * 输出token单价
   */
  outputTokenUnitPrice?: number
  /**
   * 平台
   */
  platform?: string
  /**
   * 设置参数
   */
  setting?: string
  /**
   * 状态 0-未检测 1-通过 2-失败
   */
  status?: number
  /**
   * 输入token单价
   */
  tokenUnitPrice?: number
  /**
   * 模型类型 text,image,embedding,rerank
   */
  type?: 'text' | 'image' | 'embedding' | 'rerank'
  capabilities?: string[]
  pinned?: 0 | 1
}
// 获取可用的模型
export function getBizAiModelAvailableModels(
  data: { type: 'text' | 'image' | 'embedding' | 'rerank' } | undefined = { type: 'text' },
) {
  return get<AIModelVo[]>({
    url: '/biz/aiModel/availableModels',
    data,
  })
}

// 模型厂商配置列表
export function getBizModelTreeList<T>(data: any) {
  return get<T>({
    url: '/biz/model/tree/list',
    data,
  })
}

// 对话引用-片段列表
export function getBizChatContentRecordList<T>(data: {
  chatPrimaryKey: string
  embeddingIdList?: string
}) {
  return get<T>({
    url: '/biz/chat/content/list',
    data,
  })
}



// >>>>>>>> 游客相关接口 >>>>>>>>
/**
 * 游客创建会话
 */
export function createGuestOutwardApply(id: string) {
  return post<ConversationVo>({ url: `/biz/guest/conversation/apply?id=${id}` });
}

// 会话列表
export function getBizGuestConversationList<T>(data: any) {
  return get<T>({
    url: '/biz/guest/conversation/list',
    data,
  })
}

/**
 * 获取游客对话记录
 */
export function getBizGuestChatRecord<T>(data: any) {
  return get<T>({
    url: '/biz/guest/conversation/getChatRecord',
    data,
  })
}

/**
 * 清空游客对话上下文
 */
export function clearBizGuestContext<T>(data: any) {
  return post<T>({
    url: '/biz/guest/conversation/clearContext',
    data,
  })
}

/**
 * 获取游客会话详情
 */
export function getBizGuestConversationDetail<T>(data: any) {
  return get<T>({
    url: '/biz/guest/conversation/detail',
    data,
  })
}

/**
 * 游客对话
 */
export function bizGuestConversationCompletions<T>(data: any) {
  return post<T>({
    url: '/biz/guest/conversation/completions',
    data,
  })
}

/**
 * 游客对话重试
 */
export function bizGuestConversationRetry<T>(data: any) {
  return post<T>({
    url: '/biz/guest/conversation/retry',
    data,
  })
}

/**
 * 编辑游客对话
 */
export function updateBizGuestChat<T>(data: any) {
  return post<T>({
    url: '/biz/guest/conversation/chat/update',
    data,
  })
}

/**
 * 删除会话
 */
export function removeBizGuestConversation<T>(data: Record<string, any>) {
  return post<T>({
    url: `/biz/guest/conversation/remove?id=${data.id}`,
    data,
  })
}

// /biz/guest/conversation/updateConversation
// 修改会话
export function updateBizGuestConversation<T>(data: any) {
  return post<T>({
    url: '/biz/guest/conversation/updateConversation',
    data,
  })
}

// 对话引用-片段列表
export function getBizGuestChatContentRecordList<T>(data: {
  chatPrimaryKey: string
  embeddingIdList?: string
}) {
  return get<T>({
    url: '/biz/guest/conversation/chat/content/list',
    data,
  })
}

// 消息摘要
export function getBizGuestConversationMessageSummary<T>(data: any) {
  return get<T>({
    url: '/biz/guest/conversation/messageSummary',
    data,
  })
}
// <<<<<<<<< 游客相关接口 <<<<<<<<<