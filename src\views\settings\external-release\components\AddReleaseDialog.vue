<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import { saveOutward, updateOutward } from '@/api/external-release'
import { OutwardType, OutwardVo } from '@/api/external-release/type'
import type { OutwardSaveParam } from '@/api/external-release/type'
import webTypeIcon from '@/assets/settings/external-release/web-type-icon.png'
import wechatBotTypeIcon from '@/assets/settings/external-release/wechat-bot-type-icon.png'
import ddBotTypeIcon from '@/assets/settings/external-release/dd-bot-type-icon.png'
import feishuBotTypeIcon from '@/assets/settings/external-release/feishu-bot-type-icon.png'
import { formatDate } from '@/utils/date'
import dayjs from 'dayjs'
import { useAgentList } from '@/hooks/useAgentList'

defineOptions({
  name: 'AddWebReleaseDialog',
})

const props = defineProps<{
  outwardType?: OutwardType
}>()

const emits = defineEmits(['updateReleaseList'])

const dialogTitle = ref('新建')
const dialogVisible = defineModel({ default: false })
let id = ref(0)
/**
 * 是否是编辑
 */
const isEdit = computed(() => !!id.value)
const ruleFormRef = ref<FormInstance>()
const submitLoading = ref(false)
const outType = ref<OutwardType>(OutwardType.Web)
watchEffect(() => {
  if (props.outwardType) outType.value = props.outwardType
})
const validity = ref(-1)
function handleValidityChange(val: number) {
  switch (val) {
    // case 1:
    // case 3:
    // case 7:
    //   form.deadline = dayjs().add(val, 'day').toDate()
    //   break
    case 0:
      form.deadline = dayjs('2099-12-31 00:00:00').toDate()
      break
    case -1:
      form.deadline = undefined
      break
    default:
      form.deadline = dayjs().add(val, 'day').toDate()
      break
  }
  // 值改变以后，主动检验 有效期的文本框，避免因为失焦改变，导致出现表单验证失败
  if (ruleFormRef.value && val !== -1) {
    ruleFormRef.value.validateField('deadline')
  }
}

const tokens = ref(0)
const selectToken = ref(-1)
/**
 * 剩余 token 数
 */
const remainTokens = computed(() => {
  if (selectToken.value === 0) {
    return '∞'
  }
  const maxTokensVal = form.maxTokens === undefined ? 0 : Number(form.maxTokens)
  return maxTokensVal - tokens.value
})
function handleChangeToken(val: number) {
  switch (val) {
    case 0:
      form.maxTokens = '∞'
      break
    case -1:
      form.maxTokens = undefined
      break
    default:
      form.maxTokens = val
      break
  }

  // 值改变以后，主动检验 限制模型消耗的文本框，避免因为失焦改变，导致出现表单验证失败
  if (ruleFormRef.value && val !== -1) {
    ruleFormRef.value.validateField('maxTokens')
  }
}

const form = reactive<{
  name: string
  agentId?: number
  deadline?: Date
  maxTokens?: number | string
  payload: { [key: string]: string }
  remark: string
}>({
  name: '',
  agentId: undefined,
  deadline: undefined,
  maxTokens: undefined,
  payload: {
    password: '',
  },
  remark: '',
})

interface RuleForm {
  name: string
  agentId: number
  deadline: Date
  maxTokens: number
  remark?: string
  payload: { [key: string]: string }
}

const rules = reactive<FormRules<RuleForm>>({
  name: [
    { required: true, message: '请输入对话名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  agentId: [{ required: true, message: '请选择关联助手', trigger: 'change' }],
  deadline: [{ required: true, message: '请选择有效期', trigger: 'change' }],
  maxTokens: [
    { required: true, message: '请输入模型消耗上限', trigger: 'blur' },
    {
      validator: (_, value) => {
        if (value === '∞' || value === ' ∞') return true
        const num = Number(value)
        if (!isNaN(num) && num > 0) return true
        return new Error('请输入大于0的数字或∞')
      },
      trigger: 'blur',
    },
  ],
  'payload.token': [{ required: true, message: '请输入Token', trigger: 'blur' }],
  'payload.encodingAESKey': [{ required: true, message: '请输入Encoding-AESKey', trigger: 'blur' }],
  'payload.appKey': [{ required: true, message: '请输入appKey', trigger: 'blur' }],
  'payload.appSecret': [{ required: true, message: '请输入appSecret', trigger: 'blur' }],
  'payload.templateId': [{ required: true, message: '请输入templateId', trigger: 'blur' }],
  'payload.appId': [{ required: true, message: '请输入appId', trigger: 'blur' }],
})

const { agentList, getAgentList } = useAgentList()
/**
 * 当前选中的助手对象
 */
const selectedAgent = computed(() => {
  return agentList.value.find(item => item.id === form.agentId)
})
watchEffect(() => {
  if (dialogVisible.value) {
    getAgentList()
  }
})

/**
 * 重置表单数据
 */
function resetForm() {
  dialogTitle.value = '新建'
  id.value = 0
  if (ruleFormRef.value) {
    ruleFormRef.value.resetFields()
  }
  form.name = ''
  form.agentId = undefined
  form.deadline = undefined
  form.maxTokens = undefined
  form.remark = ''
  form.payload = {
    password: '',
  }

  validity.value = -1
  selectToken.value = -1
  tokens.value = 0
}

/**
 * 根据限制模型消耗的输入值，回填单选框的值
 */
function handleMaxTokensInput(val?: string | number) {
  switch (val) {
    case 50000:
    case 100000:
    case 200000:
      selectToken.value = Number(val)
      break
    case '∞':
      selectToken.value = 0
      break
    default:
      selectToken.value = -1
      break
  }
}

function handleBeforeClose(done: () => void) {
  if (!submitLoading.value) {
    done()
  }
}

function handleClose() {
  dialogVisible.value = false
  resetForm()
}

function handleSubmit() {
  if (ruleFormRef.value) {
    ruleFormRef.value.validate(valid => {
      if (valid) {
        const params: OutwardSaveParam = {
          agentId: form.agentId!,
          name: form.name,
          deadline: formatDate(form.deadline!, 'YYYY-MM-DD HH:mm:ss'),
          maxTokens: selectToken.value === 0 ? -1 : (form.maxTokens as number),
          outwardType: outType.value,
          payload: form.payload,
          remark: form.remark,
        }

        const modifyMethod = id.value ? updateOutward : saveOutward
        submitLoading.value = true
        if (id.value) {
          params.id = id.value
        }

        modifyMethod(params)
          .then(res => {
            if (res.code === 200) {
              ElMessage.success('创建成功')
              emits('updateReleaseList')
              handleClose()
            } else {
              ElMessage.error(res.msg || '创建失败')
            }
          })
          .finally(() => {
            submitLoading.value = false
          })
      }
    })
  }
}

function generateRandomPassword() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!#$'
  const length = 4 // 4位长度
  let password = ''
  const cryptoArray = new Uint32Array(length)
  window.crypto.getRandomValues(cryptoArray)

  for (let i = 0; i < length; i++) {
    password += chars[cryptoArray[i] % chars.length]
  }
  return password
}

/**
 * 编辑
 */
function edit(outwardItem: OutwardVo) {
  dialogTitle.value = '编辑'
  id.value = outwardItem.id || 0

  form.name = outwardItem.name || ''
  form.agentId = outwardItem.agentId
  form.deadline = dayjs(outwardItem.deadline).toDate()
  form.maxTokens = outwardItem.maxTokens === -1 ? '∞' : outwardItem.maxTokens
  form.remark = outwardItem.remark || ''
  outType.value = outwardItem.outwardType.code
  switch (outType.value) {
    case OutwardType.Web:
      form.payload = outwardItem.payload || { password: '' }
      break
    case OutwardType.Wechat:
      form.payload = outwardItem.payload || { token: '', encodingAESKey: '' }
      break
    case OutwardType.Dingtalk:
      form.payload = outwardItem.payload || { appKey: '', appSecret: '', templateId: '' }
      break
    case OutwardType.Feishu:
      form.payload = outwardItem.payload || { appId: '', appSecret: '' }
      break
  }
  validity.value = -1
  handleMaxTokensInput(form.maxTokens)
  tokens.value = outwardItem.tokens || 0
}

defineExpose({
  edit,
})
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    width="900"
    class="release-type-dialog"
    :close-on-click-modal="false"
    :before-close="handleBeforeClose"
    @close="handleClose"
  >
    <template #header>
      <span class="text-[24px] g-family-medium">{{ dialogTitle }}</span>
    </template>
    <div class="release-type-hero" v-if="outwardType === OutwardType.Web">
      <img class="size-16 mr-4" :src="webTypeIcon" alt="网页对话" />
      <div>
        <div class="g-family-medium text-[16px]">网页对话</div>
        <div class="text-[#646A73] text-[14px]">
          说明：发布后，将获取一个网页地址，用户打开后可直接进行AI对话。
        </div>
      </div>
    </div>
    <div class="release-type-hero" v-if="outwardType === OutwardType.Wechat">
      <img class="size-16 mr-4" :src="wechatBotTypeIcon" alt="企微机器人" />
      <div>
        <div class="g-family-medium text-[16px]">企微机器人</div>
        <div class="text-[#646A73] text-[14px]">
          说明：对接企业微信的机器人，直接在企微中与机器人对话。
          <a
            class="text-[#4865E8] ml-2 cursor-pointer"
            href="https://developer.work.weixin.qq.com/document/path/101039"
            target="_blank"
            >查看对接帮助→</a
          >
        </div>
      </div>
    </div>
    <div class="release-type-hero" v-if="outwardType === OutwardType.Dingtalk">
      <img class="size-16 mr-4" :src="ddBotTypeIcon" alt="钉钉机器人" />
      <div>
        <div class="g-family-medium text-[16px]">钉钉机器人</div>
        <div class="text-[#646A73] text-[14px]">
          说明：对接钉钉的机器人，直接在钉钉中与机器人对话或将机器人加入群聊，进行对话。
          <a
            class="text-[#4865E8] ml-2 cursor-pointer"
            href="https://open.dingtalk.com/document/orgapp/the-creation-and-installation-of-the-application-robot-in-the"
            target="_blank"
            >查看对接帮助→</a
          >
        </div>
      </div>
    </div>
    <div class="release-type-hero" v-if="outwardType === OutwardType.Feishu">
      <img class="size-16 mr-4" :src="feishuBotTypeIcon" alt="飞书机器人" />
      <div>
        <div class="g-family-medium text-[16px]">飞书机器人</div>
        <div class="text-[#646A73] text-[14px]">
          说明：对接飞书的机器人，直接在飞书中与机器人对话或将机器人加入群聊，进行对话。
          <a
            class="text-[#4865E8] ml-2 cursor-pointer"
            href="https://open.feishu.cn/document/develop-an-echo-bot/introduction"
            target="_blank"
            >查看对接帮助→</a
          >
        </div>
      </div>
    </div>
    <el-form
      ref="ruleFormRef"
      :model="form"
      :rules="rules"
      require-asterisk-position="right"
      label-width="auto"
      label-position="top"
      @submit.prevent
    >
      <el-form-item prop="name">
        <template #label>
          <span class="g-family-medium text-[16px]">名称</span>
        </template>
        <el-input v-model="form.name" :maxlength="20" show-word-limit placeholder="请输入" />
      </el-form-item>

      <el-form-item prop="agentId">
        <template #label>
          <span class="g-family-medium text-[16px]">关联助手</span>
        </template>
        <Selector
          class="model-selector"
          v-model="form.agentId"
          :opentionList="agentList"
          :group-label="false"
          :disabled="isEdit"
          placeholder="请选择关联助手"
        >
          <template #logo="{ option }">
            <div
              v-if="option"
              class="emoji-wrap !size-7 !inline-flex mr-2"
              :style="{ backgroundColor: option.backgroundColor }"
            >
              <img class="emoji" :src="option.emoji" alt="" />
            </div>
          </template>
        </Selector>
        <!-- <el-select
          v-model="form.agentId"
          :disabled="isEdit"
          placeholder="请选择关联助手"
          style="width: 100%"
        >
          <el-option
            v-for="item in agentList"
            :key="item.id"
            :label="item.name"
            :value="item.id!"
          />
        </el-select> -->
      </el-form-item>
      <div class="text-[#969BA4] mt-[-12px] mb-6">只可关联已审核上架的助手，私人助手不可发布</div>
      <div v-if="selectedAgent" class="agent-info w-full mb-6 mt-[-12px]">
        <div class="emoji-wrap" :style="{ backgroundColor: selectedAgent.backgroundColor }">
          <img class="emoji" :src="selectedAgent.emoji" alt="" />
        </div>
        <div v-if="selectedAgent" class="ml-4 flex-1">
          <div class="agent-info-name g-family-medium">
            {{ selectedAgent.name }}
          </div>
          <div class="agent-info-create-info">
            {{ selectedAgent.createUserName }}&nbsp;|&nbsp; {{ selectedAgent.createTime }}
          </div>
          <div class="agent-info-description">
            {{ selectedAgent.description }}
          </div>
        </div>
      </div>

      <el-form-item prop="deadline">
        <template #label>
          <span class="g-family-medium text-[16px]">有效期</span>
        </template>
        <el-radio-group class="mb-3" v-model="validity" @change="handleValidityChange">
          <el-radio :value="1">1天</el-radio>
          <el-radio :value="3">3天</el-radio>
          <el-radio :value="7">7天</el-radio>
          <el-radio :value="0">永久</el-radio>
          <el-radio :value="-1">自定义</el-radio>
        </el-radio-group>
        <!-- :disabled-date="(time: Date) => time.getTime() <= Date.now()" -->
        <el-date-picker
          v-model="form.deadline"
          type="datetime"
          format="YYYY-MM-DD HH:mm:ss"
          date-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择有效期"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item prop="maxTokens">
        <template #label>
          <span class="g-family-medium text-[16px]">限制模型消耗</span>
        </template>
        <el-radio-group class="mb-3" v-model="selectToken" @change="handleChangeToken">
          <el-radio :value="50000">50K</el-radio>
          <el-radio :value="100000">100K</el-radio>
          <el-radio :value="200000">200K</el-radio>
          <el-radio :value="0">不限制</el-radio>
          <el-radio :value="-1">自定义</el-radio>
        </el-radio-group>
        <el-input
          v-model="form.maxTokens"
          :style="{ fontSize: selectToken === 0 ? '20px' : '' }"
          @input="handleMaxTokensInput"
        >
          <template #append><span style="font-size: 16px">Tokens</span></template>
        </el-input>
      </el-form-item>
      <div class="text-[#969BA4] mt-[-12px] mb-6">
        已累计消耗 <span class="text-[#4865E8]">{{ tokens || 0 }}</span> tokens，剩余<span
          class="mx-1"
          :style="{ fontSize: selectToken === 0 ? '20px' : '' }"
          >{{ remainTokens }}</span
        >tokens
      </div>

      <template v-if="outType === OutwardType.Web">
        <el-form-item prop="payload.password">
          <template #label>
            <span class="g-family-medium text-[16px]">访问密码</span>
          </template>
          <div class="flex items-center w-full">
            <div class="flex-1">
              <el-input style="width: 100%" v-model="form.payload.password" :min="0"> </el-input>
            </div>
            <button class="random-btn" @click="form.payload.password = generateRandomPassword()">
              <el-icon size="18" color="#4865E8"><RefreshLeft /></el-icon>
            </button>
          </div>
        </el-form-item>
        <div class="text-[#969BA4] mt-[-12px] mb-6">当密码留空，用户则不需要密码即可访问使用</div>
      </template>

      <template v-if="outType === OutwardType.Wechat">
        <el-form-item prop="payload.token">
          <template #label>
            <span class="g-family-medium text-[16px]">Token</span>
          </template>
          <div class="flex items-center w-full">
            <el-input style="width: 100%" v-model="form.payload.token"> </el-input>
          </div>
        </el-form-item>
        <div class="text-[#969BA4] mt-[-12px] mb-6">
          从企微后台机器人获取。<a
            class="text-[#4865E8] cursor-pointer"
            href="https://developer.work.weixin.qq.com/document/path/101039"
            target="_blank"
            >查看对接说明</a
          >
        </div>

        <el-form-item prop="payload.encodingAESKey">
          <template #label>
            <span class="g-family-medium text-[16px]">Encoding-AESKey</span>
          </template>
          <div class="flex items-center w-full">
            <el-input style="width: 100%" v-model="form.payload.encodingAESKey"> </el-input>
          </div>
        </el-form-item>
        <div class="text-[#969BA4] mt-[-12px] mb-6">
          从企微后台机器人获取。<a
            class="text-[#4865E8] cursor-pointer"
            href="https://developer.work.weixin.qq.com/document/path/101039"
            target="_blank"
            >查看对接说明</a
          >
        </div>
      </template>

      <template v-if="outType === OutwardType.Dingtalk">
        <el-form-item prop="payload.appKey">
          <template #label>
            <span class="g-family-medium text-[16px]">appKey</span>
          </template>
          <div class="flex items-center w-full">
            <el-input style="width: 100%" v-model="form.payload.appKey"> </el-input>
          </div>
        </el-form-item>
        <div class="text-[#969BA4] mt-[-12px] mb-6">
          从钉钉后台获取应用的 Client ID。<a
            class="text-[#4865E8] cursor-pointer"
            href="https://open.dingtalk.com/document/orgapp/basic-concepts-beta#title-g14-h6j-ff5"
            target="_blank"
            >查看对接说明</a
          >
        </div>

        <el-form-item prop="payload.appSecret">
          <template #label>
            <span class="g-family-medium text-[16px]">appSecret</span>
          </template>
          <div class="flex items-center w-full">
            <el-input style="width: 100%" v-model="form.payload.appSecret"> </el-input>
          </div>
        </el-form-item>
        <div class="text-[#969BA4] mt-[-12px] mb-6">
          从钉钉开发平台获取应用的 Client Secret。<a
            class="text-[#4865E8] cursor-pointer"
            href="https://open.dingtalk.com/document/orgapp/basic-concepts-beta#title-g14-h6j-ff5"
            target="_blank"
            >查看对接说明</a
          >
        </div>

        <el-form-item prop="payload.templateId">
          <template #label>
            <span class="g-family-medium text-[16px]">templateId</span>
          </template>
          <div class="flex items-center w-full">
            <el-input style="width: 100%" v-model="form.payload.templateId"> </el-input>
          </div>
        </el-form-item>
        <div class="text-[#969BA4] mt-[-12px] mb-6">
          从钉钉开发平台创建并发布AI卡片获取模板id。<a
            class="text-[#4865E8] cursor-pointer"
            href="https://open.dingtalk.com/document/orgapp/ai-card-template"
            target="_blank"
            >查看对接说明</a
          >
        </div>
      </template>

      <template v-if="outType === OutwardType.Feishu">
        <el-form-item prop="payload.appId">
          <template #label>
            <span class="g-family-medium text-[16px]">appId</span>
          </template>
          <div class="flex items-center w-full">
            <el-input style="width: 100%" v-model="form.payload.appId"> </el-input>
          </div>
        </el-form-item>
        <div class="text-[#969BA4] mt-[-12px] mb-6">
          从飞书后台获取应用的 App ID。<a
            class="text-[#4865E8] cursor-pointer"
            href="https://open.feishu.cn/document/develop-an-echo-bot/faq"
            target="_blank"
            >查看对接说明</a
          >
        </div>

        <el-form-item prop="payload.appSecret">
          <template #label>
            <span class="g-family-medium text-[16px]">appSecret</span>
          </template>
          <div class="flex items-center w-full">
            <el-input style="width: 100%" v-model="form.payload.appSecret"> </el-input>
          </div>
        </el-form-item>
        <div class="text-[#969BA4] mt-[-12px] mb-6">
          从飞书开发平台获取应用的 App Secret。<a
            class="text-[#4865E8] cursor-pointer"
            href="https://open.feishu.cn/document/develop-an-echo-bot/faq"
            target="_blank"
            >查看对接说明</a
          >
        </div>
      </template>

      <el-form-item prop="remark">
        <template #label>
          <span class="g-family-medium text-[16px]">备注</span>
        </template>
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="4"
          :maxlength="200"
          showWordLimit
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button :disabled="submitLoading" @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ submitLoading ? '保存中...' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
.release-type-hero {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 12px 16px;

  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.random-btn {
  width: 62px;
  height: 40px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #4865e8;
  margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.agent-info {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #dadde8;
  // &-avatar {

  // }

  &-name {
    font-size: 18px;
    margin-top: 10px;
  }

  &-create-info {
    font-size: 14px;
    color: #969ba4;
    margin-top: 8px;
  }

  &-description {
    font-size: 14px;
    color: #646a73;
    margin-top: 16px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
:deep(.model-selector) {
  height: 40px;
}
</style>
<style lang="less">
.release-type-dialog {
  padding: 24px;
  .el-form-item {
    margin-bottom: 24px;
  }
}
</style>
