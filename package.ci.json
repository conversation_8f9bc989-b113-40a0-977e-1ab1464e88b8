{"name": "ease-ai", "version": "2.11.1", "private": false, "description": "Ease AI", "scripts": {"build": "npm run update-build-info && vite build", "build:stage": "npm run update-build-info && vite build --mode stage", "build:mobile": "npm run update-build-info && vite build --mode mobile", "update-build-info": "node -e \"const fs = require('fs'); const buildInfo = { buildTime: new Date().toISOString(), description: 'Build information for version checking' }; fs.writeFileSync('./public/build-info.json', JSON.stringify(buildInfo, null, 2));\""}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@formkit/auto-animate": "^0.8.2", "@vscode/markdown-it-katex": "^1.0.3", "@vueuse/core": "^9.13.0", "await-to-js": "^3.0.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.3", "highlight.js": "^11.7.0", "html-to-image": "^1.11.11", "image-conversion": "^2.1.1", "jsencrypt": "^3.3.2", "katex": "^0.16.4", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "markdown-it": "^13.0.1", "markdown-it-footnote": "^4.0.0", "md-editor-v3": "^5.3.2", "mermaid-it-markdown": "^1.0.8", "naive-ui": "^2.34.3", "pinia": "^2.0.33", "vite-svg-loader": "^5.1.0", "vue": "^3.2.47", "vue-cropper": "^1.1.1", "vue-draggable-plus": "^0.6.0", "vue-echarts": "^7.0.3", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vue-types": "^5.1.3", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-emoji-picker": "^1.1.8"}, "devDependencies": {"@iconify/vue": "^4.1.0", "@types/katex": "^0.16.0", "@types/lodash": "^4.17.20", "@types/markdown-it": "^12.2.3", "@types/markdown-it-footnote": "^3.0.4", "@types/node": "^18.14.6", "@vitejs/plugin-vue": "^4.0.0", "autoprefixer": "^10.4.13", "axios": "^1.3.4", "crypto-js": "^4.1.1", "dotenv": "^16.4.7", "less": "^4.1.3", "markdown-it-link-attributes": "^4.0.1", "postcss": "^8.4.21", "tailwindcss": "^3.2.7", "typescript": "~5.8.3", "unplugin-auto-import": "0.16.7", "unplugin-vue-components": "0.25.2", "vite": "^4.2.0", "vite-plugin-pwa": "^0.14.4", "vue-tsc": "^3.0.5"}}