#!/usr/bin/env node

/**
 * 云效专用构建脚本
 * 解决 electron 依赖安装问题
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始云效构建流程...');

// 1. 设置环境变量
process.env.ELECTRON_SKIP_BINARY_DOWNLOAD = '1';
process.env.PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD = '1';
process.env.NPM_CONFIG_REGISTRY = 'https://registry.npmmirror.com/';

console.log('✅ 环境变量设置完成');

// 2. 备份原始 package.json
const originalPackageJson = fs.readFileSync('package.json', 'utf8');
fs.writeFileSync('package.json.backup', originalPackageJson);

console.log('✅ 备份原始 package.json');

try {
  // 3. 使用云效专用的 package.json
  if (fs.existsSync('package.ci.json')) {
    fs.copyFileSync('package.ci.json', 'package.json');
    console.log('✅ 使用云效专用 package.json');
  }

  // 4. 清理 node_modules
  if (fs.existsSync('node_modules')) {
    console.log('🧹 清理旧的 node_modules...');
    execSync('rm -rf node_modules', { stdio: 'inherit' });
  }

  // 5. 安装依赖
  console.log('📦 安装依赖...');
  execSync('npm install --production=false --ignore-scripts', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      ELECTRON_SKIP_BINARY_DOWNLOAD: '1',
      PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: '1'
    }
  });

  // 6. 生成构建信息
  console.log('📝 生成构建信息...');
  const buildInfo = {
    buildTime: new Date().toISOString(),
    description: 'Build information for version checking'
  };
  
  if (!fs.existsSync('public')) {
    fs.mkdirSync('public', { recursive: true });
  }
  
  fs.writeFileSync('./public/build-info.json', JSON.stringify(buildInfo, null, 2));

  // 7. 执行构建
  console.log('🔨 开始构建...');
  execSync('npx vite build --mode stage', { stdio: 'inherit' });

  console.log('🎉 构建成功完成！');

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  process.exit(1);
} finally {
  // 8. 恢复原始 package.json
  if (fs.existsSync('package.json.backup')) {
    fs.copyFileSync('package.json.backup', 'package.json');
    fs.unlinkSync('package.json.backup');
    console.log('✅ 恢复原始 package.json');
  }
}
