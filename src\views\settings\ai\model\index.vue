<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import DefaultSettingsPopover from './components/DefaultSettingsPopover/index.vue'
import PlatformSidebar from './components/PlatformSidebar/index.vue'
import PlatformContent from './components/PlatformContent/PlatformInfo.vue'
import PlatformModel from './components/PlatformContent/PlatformModel.vue'
import ModelPlatformDialog from './components/ModelPlatformDialog.vue'
import DetectionModelDialog from './components/DetectionModelDialog.vue'
import ModelListDialog from './components/ModelListDialog.vue'
import AddModelDialog from './components/AddModelDialog.vue'
import EditModelDialog from './components/EditModelDialog.vue'
import { PlatformInfo } from '@/api/model-base/types'
import { getAiPlatformDetail, getAiPlatformList } from '@/api/model-base'
import { Component } from 'vue'
import { getBizAiModelListByPlatformGrouped } from '@/api/model'
import { AIModelVo } from '@/api'

function useHeaderActions(headComp: Component) {
  const headerActions = inject<Ref<any>>('headerActions')
  onMounted(() => {
    if (headerActions) {
      headerActions.value = h(headComp)
    }
  })
  onUnmounted(() => {
    if (headerActions) {
      headerActions.value = null
    }
  })
}

useHeaderActions(DefaultSettingsPopover)

function usePlatformList() {
  const currentPlatformId = ref<string | number>()

  const platforms = ref<PlatformInfo[]>([])
  const loading = ref(false)

  /**
   * 获取平台列表
   */
  async function getPlatformList() {
    loading.value = true
    try {
      const res = await getAiPlatformList()
      if (res.code === 200) {
        platforms.value = res.rows
        if (
          !currentPlatformId.value ||
          !platforms.value.find(item => item.id === currentPlatformId.value)
        ) {
          currentPlatformId.value = platforms.value[0]?.id
        }
      }
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }
  getPlatformList()

  /**
   * 没有数据时，展示的文字
   */
  const noDataText = computed(() => {
    if (platforms.value.length) {
      return '请选择平台'
    } else {
      return '暂无数据'
    }
  })

  return {
    currentPlatformId,
    platforms,
    loading,
    getPlatformList,
    noDataText,
  }
}

const { currentPlatformId, platforms, loading, getPlatformList, noDataText } = usePlatformList()

function usePlatformInfo() {
  const platformContentRef = useTemplateRef('platformContentRef')
  const platformInfo = ref<PlatformInfo>()
  const platformLogo = computed(() => platformInfo.value?.icon)

  /**
   * 获取平台信息
   */
  function getPlatformInfo() {
    if (currentPlatformId.value) {
      return getAiPlatformDetail<PlatformInfo>(currentPlatformId.value).then(res => {
        if (res.code === 200) {
          platformInfo.value = res.data
        }
        return res
      })
    }
    return Promise.reject(false)
  }

  watch(
    () => currentPlatformId.value,
    (newVal, oldVal) => {
      if (newVal !== oldVal) {
        getPlatformInfo()
      }
    },
    { immediate: true },
  )

  return {
    getPlatformInfo,
    platformInfo,
    platformLogo,
    platformContentRef,
  }
}

const { getPlatformInfo, platformInfo, platformLogo, platformContentRef } = usePlatformInfo()

/**
 * 平台模型列表
 */
function usePlatformModel() {
  const textModelList = ref<AIModelVo[]>([])
  const embeddingModelList = ref<AIModelVo[]>([])
  const rerankModelList = ref<AIModelVo[]>([])

  const modelListLoading = ref(false)

  const allModelList = computed(() => [
    ...textModelList.value,
    ...embeddingModelList.value,
    ...rerankModelList.value,
  ])
  /**
   * 获取模型列表
   */
  function getModelList() {
    if (currentPlatformId.value) {
      textModelList.value = []
      embeddingModelList.value = []
      rerankModelList.value = []
      modelListLoading.value = true
      getBizAiModelListByPlatformGrouped<{
        text: AIModelVo[]
        embedding: AIModelVo[]
        rerank: AIModelVo[]
      }>(currentPlatformId.value)
        .then(res => {
          if (res.code === 200) {
            textModelList.value = res.data.text
            embeddingModelList.value = res.data.embedding
            rerankModelList.value = res.data.rerank
          }
        })
        .finally(() => {
          modelListLoading.value = false
        })
    }
  }
  watchEffect(() => {
    getModelList()
  })

  return {
    textModelList,
    embeddingModelList,
    rerankModelList,
    modelListLoading,
    allModelList,
    getModelList,
  }
}

const {
  getModelList,
  textModelList,
  embeddingModelList,
  rerankModelList,
  modelListLoading,
  allModelList,
} = usePlatformModel()

/**
 * 更新平台列表 和 平台内容
 */
function updatePlatformListAndContent(id: string) {
  currentPlatformId.value = id
  getPlatformList()
  getPlatformInfo()
  // if (id === currentPlatformId.value) {
  // }
}

// 新增 模型平台 弹窗
function useModelPlatformDialog() {
  const modelPlatformVisable = ref(false)
  const modelPlatformDialogRef = ref()

  function openPlatformDialog() {
    modelPlatformVisable.value = true
  }
  function editPlatform(platform: PlatformInfo) {
    if (modelPlatformDialogRef.value) {
      modelPlatformDialogRef.value.editPlatformInfo(platform)
    }
  }
  return {
    modelPlatformDialogRef,
    modelPlatformVisable,
    editPlatform,
    openPlatformDialog,
  }
}

const { modelPlatformDialogRef, modelPlatformVisable, editPlatform, openPlatformDialog } =
  useModelPlatformDialog()

// 新增 模型弹窗
const modelAddDialogVisable = ref(false)

// 编辑 模型弹窗
function useModelEditDialog() {
  const modelEditDialogVisable = ref(false)
  const modelEditDialogRef = ref<InstanceType<typeof EditModelDialog>>()
  function editModel(model: any) {
    modelEditDialogVisable.value = true

    if (modelEditDialogRef.value) {
      modelEditDialogRef.value.setModelData(model)
    }
  }
  return {
    modelEditDialogVisable,
    modelEditDialogRef,
    editModel,
  }
}
const { editModel, modelEditDialogVisable, modelEditDialogRef } = useModelEditDialog()

// 检测模型 弹窗
const modelCheckVisable = ref(false)
/**
 * 检测模型
 * 1. 校验 api 地址 和 api 密钥 是否填写
 */
function checkModel() {
  modelCheckVisable.value = true
}

// 模型列表 弹窗
const modelListVisable = ref(false)

function openModelListDialog() {
  if (!platformInfo.value?.baseUrl) {
    return ElMessage.warning('请先配置平台的 API 地址')
  }
  modelListVisable.value = true
}
</script>

<template>
  <div class="model-base-container">
    <PlatformSidebar
      v-model="currentPlatformId"
      :platforms="platforms"
      :loading="loading"
      @open="openPlatformDialog"
      @editPlatform="editPlatform"
      @updateInfo="getPlatformList"
    />
    <div v-if="platformInfo?.id" class="model-platform-container">
      <PlatformContent
        ref="platformContentRef"
        :currentPlatformId="platformInfo?.id"
        :hasPlatform="!!platforms.length"
        :platformLogo="platformLogo"
        :apiKey="platformInfo?.apiKey"
        :baseUrl="platformInfo?.baseUrl"
        :platformName="platformInfo?.platformName"
        :isEnabled="Boolean(platformInfo?.isEnabled)"
        :isDefault="platformInfo?.isDefault"
        :defaultBaseUrl="platformInfo?.defaultBaseUrl"
        @detection="checkModel"
        @updatePlatformList="getPlatformList"
        @updatePlatfromInfo="getPlatformInfo"
      />
      <PlatformModel
        :textModelList="textModelList"
        :embeddingModelList="embeddingModelList"
        :rerankModelList="rerankModelList"
        :loading="modelListLoading"
        @getAvaliableModelList="openModelListDialog"
        @addModel="modelAddDialogVisable = true"
        @editModel="editModel"
        @updateModelList="getModelList"
      />
    </div>
    <el-empty v-if="!currentPlatformId" :description="noDataText" />
  </div>
  <ModelPlatformDialog
    ref="modelPlatformDialogRef"
    v-model="modelPlatformVisable"
    @updateInfo="updatePlatformListAndContent"
  />
  <DetectionModelDialog v-model="modelCheckVisable" :modelList="allModelList" />
  <ModelListDialog
    v-model="modelListVisable"
    :platformId="currentPlatformId"
    :textModelList="textModelList"
    :embeddingModelList="embeddingModelList"
    :rerankModelList="rerankModelList"
    @updateModelList="getModelList"
  />
  <AddModelDialog
    v-model="modelAddDialogVisable"
    :platformId="currentPlatformId"
    @updateModelList="getModelList"
  />
  <EditModelDialog
    ref="modelEditDialogRef"
    v-model="modelEditDialogVisable"
    @updateModelList="getModelList"
  />
</template>

<style scoped lang="less">
.model-base-container {
  height: 100%;
  display: grid;
  grid-template-columns: 280px 1fr;
}

.model-platform-container {
  height: 100%;
  padding: 24px;

  display: flex;
  flex-direction: column;
}
</style>
