import { postBizTokenUsageAggregatePage } from "@/api/usage"
import { AggregateDataPoint, TokenUsageAggregatePageQueryBo } from "@/api/usage/types"
import { TableInstance } from "element-plus"

export function useAggregatePage() {
  const tableRef = ref<TableInstance | undefined>()
  const loading = ref(false)
  const tableData = ref<AggregateDataPoint[]>([])
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  function getTokenUsageAggregatePage(params: TokenUsageAggregatePageQueryBo) {
    loading.value = true
    return postBizTokenUsageAggregatePage(params).then(res => {
      if (res.code === 200) {
        tableData.value = res.rows || []
        total.value = res.total
      }
    }).catch(error => {
      console.error('获取聚合表格数据失败:', error)
      return error
    }).finally(() => { loading.value = false })
  }


  return {
    tableRef,
    loading,
    tableData,
    currentPage,
    pageSize,
    total,
    getTokenUsageAggregatePage
  }
}