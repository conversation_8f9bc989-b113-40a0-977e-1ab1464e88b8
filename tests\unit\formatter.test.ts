import { describe, it, expect } from 'vitest'
import { formatToken } from '@/utils/formatter'

describe('formatToken', () => {
  it('应正确格式化小于100000的数字', () => {
    expect(formatToken(0)).toBe('0')
    expect(formatToken(1234)).toBe('1,234')
    expect(formatToken(99999)).toBe('99,999')
  })

  it('应正确格式化大于等于100000的数字', () => {
    expect(formatToken(100000)).toBe('100K')
    expect(formatToken(123456)).toBe('123.46K')
    expect(formatToken(1000000)).toBe('1,000K')
  })

  it('应正确处理特殊值', () => {
    expect(formatToken(NaN)).toBe('')
    expect(formatToken(Infinity)).toBe('∞')
    expect(formatToken(-Infinity)).toBe('-∞')
  })

  it('应正确处理边界值', () => {
    // 边界值测试
    expect(formatToken(99999)).toBe('99,999') // 刚好小于100000
    expect(formatToken(100000)).toBe('100K') // 刚好等于100000
  })

  it('应正确处理负数', () => {
    expect(formatToken(-1234)).toBe('-1,234')
    expect(formatToken(-99999)).toBe('-99,999')
    expect(formatToken(-100000)).toBe('-100K')
    expect(formatToken(-123456)).toBe('-123.46K')
  })

  it('应正确处理小数', () => {
    // 小于100000的小数会被Math.floor处理
    expect(formatToken(1234.56)).toBe('1,234')
    expect(formatToken(99999.99)).toBe('99,999')

    // 大于等于100000的小数会被toFixed(2)处理
    expect(formatToken(100000.56)).toBe('100K')
    expect(formatToken(123456.789)).toBe('123.46K')
  })
})