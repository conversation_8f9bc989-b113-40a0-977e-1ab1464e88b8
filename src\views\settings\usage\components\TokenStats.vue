<script setup lang="ts">
import { formatToken } from '@/utils/formatter'

interface Props {
  totalTokens: number
  inputTokens: number
  outputTokens: number
}

const props = defineProps<Props>()

// 使用 defineModel 来管理当前选中的标签页状态
const activeTab = defineModel<'total' | 'input' | 'output'>({ default: 'total' })

// 定义 tabs 配置数组
const tabs: { id: 'total' | 'input' | 'output'; title: string; tokenKey: string }[] = [
  { id: 'total', title: '调用总量Tokens数', tokenKey: 'totalTokens' },
  { id: 'input', title: '输入Tokens数', tokenKey: 'inputTokens' },
  { id: 'output', title: '输出Tokens数', tokenKey: 'outputTokens' },
]
</script>

<template>
  <div class="useage-type-tab-container">
    <div
      v-for="tab in tabs"
      :key="tab.id"
      class="useage-type-tab"
      :class="{ active: activeTab === tab.id }"
      role="tab"
      @click="activeTab = tab.id"
    >
      <div class="useage-type-tab-title g-family-medium">{{ tab.title }}</div>
      <div class="useage-type-tab-value g-family-medium">
        {{ formatToken(props[tab.tokenKey]) }}
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.useage-type-tab-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  background-color: #fafbff;
  margin-bottom: 24px;

  .useage-type-tab {
    border-radius: 8px;
    background-color: #fafbff;
    padding: 18px 24px;
    cursor: pointer;

    .useage-type-tab-title {
      font-size: 18px;
    }

    .useage-type-tab-value {
      font-size: 32px;
      color: #4c5cec;
    }

    &.active {
      position: relative;
      background-color: #fff;
      &::before {
        content: '';
        display: block;
        height: 4px;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        background-color: #4865e8;
      }

      &:first-child {
        &::before {
          border-top-left-radius: 100px;
        }
      }

      &:last-child {
        &::before {
          border-top-right-radius: 100px;
        }
      }
    }
  }
}
</style>
