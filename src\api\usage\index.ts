import { get, post } from '@/utils/request'
import {
  TokenUsageTimeSeriesQueryBo,
  TokenUsageAggregatePageQueryBo,
  SysUserVo,
  SysDeptVo,
  TokenUsageTimeSeriesVo,
  AggregateDataPoint
} from './types'

/**
 * 获取时间序列统计数据
 * 支持按小时或按天统计，确保时间连续性（即使没有数据也显示0）
 */
export function postBizTokenUsageTimeSeries(data: TokenUsageTimeSeriesQueryBo) {
  return post<TokenUsageTimeSeriesVo>({
    url: '/biz/tokenUsage/timeSeries',
    data,
  })
}

// /**
//  * 获取聚合统计数据
//  * 支持按模型、用户、应用分组统计
//  */
// export function postBizTokenUsageAggregate(data: TokenUsageAggregateQueryBo) {
//   return post<RTokenUsageAggregateVo>({
//     url: '/biz/tokenUsage/aggregate',
//     data,
//   })
// }

/**
 * 获取聚合明细分页数据（带排序）
 * 支持按模型、用户、应用分组统计
 */
export function postBizTokenUsageAggregatePage(data: TokenUsageAggregatePageQueryBo) {
  return post<{ rows: AggregateDataPoint[] }>({
    url: '/biz/tokenUsage/aggregatePage',
    data,
  })
}

/**
 * 获取部门选择框列表
 */
export function getDeptTreeSelect() {
  return get<SysDeptVo[]>({
    url: '/system/dept/optionselect',
  })
}

/**
 * 获取部门下的所有用户信息
 */
export function getDeptUserList(deptId: number | string) {
  return get<SysUserVo[]>({
    url: `/system/user/list/dept/${deptId}`,
  })
}