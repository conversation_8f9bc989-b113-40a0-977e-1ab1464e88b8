import { describe, it, expect } from 'vitest'
import { filterListBySearchQuery, handleGroupList } from '@/utils/search'
describe('filterListBySearchQuery', () => {
  const testItems = [
    { id: 1, label: 'Apple', category: 'fruit' },
    { id: 2, label: 'Banana', category: 'fruit' },
    { id: 3, label: 'Carrot', category: 'vegetable' },
    { id: 4, label: 'Broccoli', category: 'vegetable' },
    { id: 5, label: 'Orange', category: 'fruit' },
  ]

  it('当搜索查询为空时应返回所有项目', () => {
    const result = filterListBySearchQuery(testItems, '')
    expect(result).toEqual(testItems)
  })

  it('应按搜索查询过滤项目（默认标签字段）', () => {
    const result = filterListBySearchQuery(testItems, 'App')
    expect(result).toEqual([{ id: 1, label: 'Apple', category: 'fruit' }])
  })

  it('应按搜索查询过滤项目（自定义字段）', () => {
    const result = filterListBySearchQuery(testItems, 'veg', 'category')
    expect(result).toEqual([
      { id: 3, label: 'Carrot', category: 'vegetable' },
      { id: 4, label: 'Broccoli', category: 'vegetable' }
    ])
  })

  it('应处理空结果', () => {
    const result = filterListBySearchQuery(testItems, 'xyz')
    expect(result).toEqual([])
  })
})

describe('handleGroupList', () => {
  const testItems = [
    { id: 1, label: 'Apple', category: 'fruit' },
    { id: 2, label: 'Banana', category: 'fruit' },
    { id: 3, label: 'Carrot', category: 'vegetable' },
    { id: 4, label: 'Broccoli', category: 'vegetable' },
    { id: 5, label: 'Orange', category: 'fruit' },
  ]
  it('处理分组列表', () => {
    expect(handleGroupList(testItems, 'category')).toEqual([
      {
        groupName: 'fruit',
        list: [
          { id: 1, label: 'Apple', category: 'fruit' },
          { id: 2, label: 'Banana', category: 'fruit' },
          { id: 5, label: 'Orange', category: 'fruit' }
        ]
      },
      {
        groupName: 'vegetable',
        list: [
          { id: 3, label: 'Carrot', category: 'vegetable' },
          { id: 4, label: 'Broccoli', category: 'vegetable' }
        ]
      }
    ])
  })

  it('当 groupName 参数为空时应返回一个分组，没有分组名字', () => {
    expect(handleGroupList(testItems, '')).toEqual([{
      groupName: '',
      list: testItems
    }])
    expect(handleGroupList(testItems, undefined)).toEqual([{
      groupName: undefined,
      list: testItems
    }])
  })

  it('当输入数组为空时应返回空数组', () => {
    expect(handleGroupList([], 'category')).toEqual([])
    expect(handleGroupList([], '')).toEqual([])
  })

  it('当所有项目都属于同一组时应正确分组', () => {
    const sameCategoryItems = [
      { id: 1, label: 'Apple', category: 'fruit' },
      { id: 2, label: 'Banana', category: 'fruit' },
      { id: 3, label: 'Orange', category: 'fruit' },
    ]
    expect(handleGroupList(sameCategoryItems, 'category')).toEqual([
      {
        groupName: 'fruit',
        list: sameCategoryItems
      }
    ])
  })

  it('当项目具有不同的分组值时应正确分组', () => {
    const diverseItems = [
      { id: 1, label: 'Apple', category: 'fruit' },
      { id: 2, label: 'Carrot', category: 'vegetable' },
      { id: 3, label: 'Milk', category: 'dairy' },
      { id: 4, label: 'Banana', category: 'fruit' },
    ]
    expect(handleGroupList(diverseItems, 'category')).toEqual([
      {
        groupName: 'fruit',
        list: [
          { id: 1, label: 'Apple', category: 'fruit' },
          { id: 4, label: 'Banana', category: 'fruit' },
        ]
      },
      {
        groupName: 'vegetable',
        list: [
          { id: 2, label: 'Carrot', category: 'vegetable' },
        ]
      },
      {
        groupName: 'dairy',
        list: [
          { id: 3, label: 'Milk', category: 'dairy' },
        ]
      }
    ])
  })

  it('当分组字段在某些项目中不存在时应正确处理', () => {
    const itemsWithMissingField = [
      { id: 1, label: 'Apple', category: 'fruit' },
      { id: 2, label: 'Banana' }, // 缺少 category 字段
      { id: 3, label: 'Carrot', category: 'vegetable' },
    ]
    expect(handleGroupList(itemsWithMissingField, 'category')).toEqual([
      {
        groupName: 'fruit',
        list: [
          { id: 1, label: 'Apple', category: 'fruit' },
        ]
      },
      {
        groupName: undefined,
        list: [
          { id: 2, label: 'Banana' },
        ]
      },
      {
        groupName: 'vegetable',
        list: [
          { id: 3, label: 'Carrot', category: 'vegetable' },
        ]
      }
    ])
  })

  it('当分组字段值包含特殊类型时应正确处理', () => {
    const itemsWithSpecialTypes = [
      { id: 1, label: 'Item 1', category: 1 },
      { id: 2, label: 'Item 2', category: '1' }, // 字符串 '1'
      { id: 3, label: 'Item 3', category: null },
      { id: 4, label: 'Item 4', category: undefined },
      { id: 5, label: 'Item 5' }, // 缺少 category 字段
    ]
    expect(handleGroupList(itemsWithSpecialTypes, 'category')).toEqual([
      {
        groupName: 1,
        list: [
          { id: 1, label: 'Item 1', category: 1 },
        ]
      },
      {
        groupName: '1',
        list: [
          { id: 2, label: 'Item 2', category: '1' },
        ]
      },
      {
        groupName: null,
        list: [
          { id: 3, label: 'Item 3', category: null },
        ]
      },
      {
        groupName: undefined,
        list: [
          { id: 4, label: 'Item 4', category: undefined },
          { id: 5, label: 'Item 5' },
        ]
      }
    ])
  })
})
