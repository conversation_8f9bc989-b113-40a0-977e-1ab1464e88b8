import { cleanup, fireEvent, render, screen, waitFor } from "@testing-library/vue";
import { describe, expect, it, afterEach, vi } from "vitest";
// 1. mock element-plus 的 useFormItem
const validateSpy = vi.fn()
vi.mock('element-plus', async () => {
  const mod = await vi.importActual<typeof import('element-plus')>('element-plus')
  return {
    ...mod,
    useFormItem: () => ({
      formItem: { validate: validateSpy }
    })
  }
})

import Selector from "@/components/Selector.vue";
import { handleGroupList } from "@/utils/search";



describe("Selector 下拉框组件测试", () => {
  // 测试数据
  const firstOptionLabel = "Model A";
  const mockOptions = [
    { label: firstOptionLabel, value: "model-a", name: "model-a", platformName: "Platform 1" },
    { label: "Model B", value: "model-b", name: "model-b", platformName: "Platform 1" },
    { label: "Model C", value: "model-c", name: "model-c", platformName: "Platform 2" },
  ];

  // 点击下拉框
  async function clickSelector() {
    const selectDom = screen.getByRole("select")
    await selectDom.click()
  }

  // 选择第一个选项
  async function selectFirstOption() {
    await clickSelector()
    const options = await screen.findAllByRole('option');
    await fireEvent.click(options[0])
  }

  afterEach(() => {
    cleanup()
  })

  // 基础渲染测试
  it("渲染下拉框 placeholder", () => {
    render(Selector, {
      props: {
        placeholder: "请选择",
      },
    });

    const input = screen.getByText("请选择");
    expect(input).toBeInTheDocument();
  });

  // 宽度属性测试
  it("测试宽度属性", () => {
    const width = 200;
    render(Selector, {
      props: {
        placeholder: "请选择",
        width,
      },
    });

    const selectorDom = screen.getByRole("select");
    expect(selectorDom).toHaveStyle(`width: ${width}px`);
  });

  // 选择功能测试
  it("点击下拉框，存在搜索框", async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
      },
    });

    await clickSelector()
    const searchBox = screen.getByLabelText("search-box");
    await waitFor(() => {
      expect(searchBox).toBeVisible();
    });
  });

  // 筛选功能测试
  it("点击下拉框，弹出下拉数量正确", async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
      },
    });

    await clickSelector()
    const options = await screen.findAllByRole("option"); // 因为有动画效果，所以这里使用 findAllByRole
    options.forEach((option) => {
      expect(option).toBeVisible();
    });
    expect(options.length).toBe(mockOptions.length);
  });

  // 搜索功能测试
  it("测试搜索功能", async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
      },
    });
    await clickSelector()
    const searchBox = screen.getByLabelText("search-box");
    await waitFor(() => {
      expect(searchBox).toBeVisible();
    });
    await fireEvent.update(searchBox, firstOptionLabel)
    const options = await screen.findAllByRole("option");
    expect(options.length).toBe(mockOptions.filter(item => item.label.includes(firstOptionLabel)).length);
  });

  it('测试未搜索到的情况', async () => {
    const { baseElement } = render(Selector, {
      props: {
        opentionList: mockOptions,
      },
    });

    await clickSelector()
    const searchBox = screen.getByLabelText("search-box");
    await waitFor(() => {
      expect(searchBox).toBeVisible();
    });

    await fireEvent.update(searchBox, 'aaaaa')
    const emptyDom = await screen.findByText('未查询到相关数据');
    expect(emptyDom).toBeVisible();
    const options = screen.queryAllByRole("option");
    expect(options.length).toBe(0);
  })

  // 搜索框清空功能测试
  it('测试搜索框清空功能', async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
      },
      global: {
        stubs: {
          transition: false
        }
      }
    });

    await clickSelector();
    const searchBox = screen.getByLabelText("search-box");
    await fireEvent.update(searchBox, firstOptionLabel);

    // 验证搜索结果
    let options = await screen.findAllByRole("option");
    expect(options.length).toBe(1);

    // 关闭下拉框
    await clickSelector();

    await waitFor(() => {
      const dropdownMenu = screen.queryByLabelText("model-dropdown", { selector: "div" });
      expect(dropdownMenu).not.toBeVisible();
    });

    // 重新打开下拉框
    await clickSelector();

    await waitFor(() => {
      const dropdownMenu = screen.queryByLabelText("model-dropdown", { selector: "div" });
      expect(dropdownMenu).toBeVisible();
    });

    // 验证搜索框被清空
    const input = screen.getByLabelText("search-box");
    expect(searchBox).toHaveValue('');

    // 验证所有选项都显示
    options = await screen.findAllByRole("option");
    expect(options.length).toBe(mockOptions.length);
  });

  // 分组功能测试
  it("测试分组功能", async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
        groupBy: "platformName",
      },
    });
    await clickSelector()
    const groupList = screen.getAllByLabelText("group");
    expect(groupList.length).toBe(handleGroupList(mockOptions, 'platformName').length);

  });

  // 分组功能边界测试
  it("测试 groupBy 为 '' 时不分组", async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
        groupBy: '',
      },
    });
    await clickSelector()
    const groupList = screen.queryAllByLabelText("group");
    expect(groupList.length).toBe(0);
  });

  // 搜索框input 的 placeholder 测试
  it("测试搜索框input 的 placeholder", async () => {
    const searchPlaceholder = "请输入搜索内容";
    render(Selector, {
      props: {
        opentionList: mockOptions,
        searchPlaceholder,
      },
    });
    await clickSelector()
    const input = await screen.findByPlaceholderText(searchPlaceholder);
    expect(input).toBeInTheDocument();
  });


  // 边界情况测试
  it("没有下拉数据的情况", async () => {
    render(Selector, {
      props: {
        opentionList: [],
      },
    });
    await clickSelector()
    const emptyDom = await screen.findByText("暂无数据");
    await waitFor(() => {
      expect(emptyDom).toBeVisible();
    });
    const options = screen.queryAllByRole("option");
    expect(options).toHaveLength(0);
  });


  // 禁用状态测试
  it("禁用状态测试, 测试禁用状态下 popover 不能打开", async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
        disabled: true,
      },
    });

    const selectorDom = screen.getByRole("select");
    expect(selectorDom).toHaveClass('is-disabled');
    await fireEvent.click(selectorDom);
    const dropdownMenu = screen.getByLabelText("model-dropdown");
    await waitFor(() => {
      expect(dropdownMenu).not.toBeVisible();
    });
  });

  // popover 显示/隐藏测试
  it("测试 popover 显示和隐藏", async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
      },
    });

    // 初始状态下 popover 应该是隐藏的
    const dropdownMenu = screen.getByLabelText("model-dropdown", {
      selector: "div",
    });
    expect(dropdownMenu).not.toBeVisible();

    // 点击触发器显示 popover
    await clickSelector();
    await waitFor(() => {
      const dropdownMenu = screen.queryByLabelText("model-dropdown", {
        selector: "div",
      });
      expect(dropdownMenu).toBeVisible();
    });

    // 点击选项后 popover 应该隐藏
    const options = await screen.findAllByRole('option');
    await fireEvent.click(options[0]);
    // 等待 popover 隐藏
    expect(dropdownMenu).not.toBeVisible();
  });

  it('下拉框选择测试', async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
      },
    });
    await selectFirstOption()
    const selectorDom = screen.getByRole("select");

    expect(selectorDom).toHaveTextContent(mockOptions[0].label)
  })

  // 模型选择事件测试
  it('测试 change 事件触发', async () => {
    const handleChange = vi.fn();
    render(Selector, {
      props: {
        opentionList: mockOptions,
        onChange: handleChange,
      },
    });

    await selectFirstOption();
    expect(handleChange).toHaveBeenCalledWith(mockOptions[0].value);
  });

  // 表单验证触发测试
  it('测试表单验证触发', async () => {
    const handleChange = vi.fn();
    render(Selector, {
      props: {
        opentionList: mockOptions,
        onChange: handleChange,
      },
    });

    await selectFirstOption();
    expect(handleChange).toHaveBeenCalledWith(mockOptions[0].value);
    expect(validateSpy).toHaveBeenCalled();
  });
  it('下拉框清除功能', async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
        clearable: true
      },
    });
    await selectFirstOption()
    const selectorDom = screen.getByRole("select");
    await fireEvent.mouseEnter(selectorDom)
    const clearIcon = screen.getByRole("button", { name: "clear" });
    await fireEvent.click(clearIcon)
    expect(selectorDom).not.toHaveTextContent(firstOptionLabel)
    expect(clearIcon).not.toBeVisible()
  })
  it('下拉框未开启清除功能', async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions
      },
    });

    await selectFirstOption()
    const selectorDom = screen.getByRole("select");
    await fireEvent.mouseEnter(selectorDom)
    const clearIcon = screen.queryByRole("button", { name: "clear" });
    expect(clearIcon).toBeNull()
  })
  it('loading 状态展示', async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions,
        loading: true
      },
    });

    const modelDropdown = screen.getByLabelText("model-dropdown", { selector: "div" });
    await waitFor(() => {
      const mask = modelDropdown.querySelector('.el-loading-mask');
      expect(mask).toBeValid();
      expect(mask).not.toHaveStyle('display: none');
    })
  });

  it('测试键盘交互 - Escape 键关闭下拉框', async () => {
    render(Selector, {
      props: {
        opentionList: mockOptions
      },
    });

    // 打开下拉框
    await clickSelector();
    let dropdownMenu = screen.getByLabelText("model-dropdown");
    await waitFor(async () => {
      expect(dropdownMenu).toBeVisible();
    })

    await fireEvent.keyDown(dropdownMenu, { key: "Escape", code: "Escape" });
    await waitFor(() => {
      expect(dropdownMenu).not.toBeVisible()
    })
  })
});