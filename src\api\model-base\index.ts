import { get, post, put, deleteReq } from '@/utils/request';
import { PlatformInfo, SortParam } from './types';

/**
 * AI平台管理模块API
 */

/**
 * 新增AI平台管理
 * @param data 平台数据
 * @returns
 */
export function addAiPlatform(data: PlatformInfo) {
	return post({
		url: '/biz/aiPlatform',
		data
	});
}

/**
 * 修改AI平台管理
 * @param data 平台数据
 * @returns
 */
export function updateAiPlatform(data: PlatformInfo) {
	return put({
		url: '/biz/aiPlatform',
		data
	});
}

/**
 * 启用/禁用平台状态
 * @param id 平台ID
 * @returns
 */
export function toggleAiPlatformStatus(id: string | number) {
	return post({
		url: `/biz/aiPlatform/toggleStatus/${id}`
	});
}

/**
 * 获取AI平台管理详细信息
 * @param id 平台ID
 * @returns
 */
export function getAiPlatformDetail<T>(id: string | number) {
	return get<T>({
		url: `/biz/aiPlatform/${id}`
	});
}

/**
 * 查询AI平台管理列表
 */
export function getAiPlatformList() {
	return get<{ rows: PlatformInfo[] }>({
		url: '/biz/aiPlatform/list'
	});
}

/**
 * 获取所有启用的平台
 * @returns
 */
export function getEnabledAiPlatforms() {
	return get({
		url: '/biz/aiPlatform/enabled'
	});
}

/**
 * 删除AI平台管理
 * @param ids 平台ID数组
 * @returns
 */
export function deleteAiPlatform(ids: (number | string)[]) {
	return deleteReq({
		url: `/biz/aiPlatform/${ids.join(',')}`
	});
}

/**
 * 批量更新平台排序
 * 请求示例：
 * PUT /biz/aiPlatform/batchSort
 * {
"sortList": [
{"id": 1, "sortOrder": 1},
{"id": 2, "sortOrder": 2},
{"id": 3, "sortOrder": 3}
]
}
 */
export function batchUpdateAiPlatformSort(data: SortParam) {
	return put({
		url: '/biz/aiPlatform/batchSort',
		data
	});
}