<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { getBizAiModelAvailableModels } from '@/api'
import { getModelLogo } from '@/utils/models'

interface ModelOption {
  displayName: string
  id: string
}

const props = defineProps<{
  modelConfig: {
    modelId: number | string
    temperature: number
    maxTokens: number
    maxContextCount: number
    singleReplyLimitFlag: boolean
  }
  disabled: boolean
}>()

const emit = defineEmits(['update:modelConfig'])

// 创建本地响应式数据，使用 props 的值作为初始值
const localConfig = ref({ ...props.modelConfig })

// 监听 props 变化
watch(
  () => props.modelConfig,
  newVal => {
    if (newVal) {
      Object.assign(localConfig.value, newVal)
    }
  },
  { deep: true, immediate: true },
)

// 监听本地数据变化
watch(
  localConfig,
  newVal => {
    emit('update:modelConfig', { ...newVal })
  },
  { deep: true },
)

// resolved: 模型列表从getBizAiModelList接口获取
const modelOptions = ref<{ label: string; value: number }[]>([])

// 获取模型列表
const fetchModelList = async () => {
  try {
    const { data } = await getBizAiModelAvailableModels()
    console.log('获取模型列表成功:', data)
    // modelOptions.value = data
    modelOptions.value = data.map(item => ({
      ...item,
      label: item.displayName,
      value: item.id,
      modelName: item.name,
    }))
    // // 如果modelId为空，则设置默认模型
    // if (!localConfig.value.modelId && modelOptions.value.length > 0) {
    //   localConfig.value.modelId = modelOptions.value[0].value
    // }
  } catch (error) {
    console.error('获取模型列表失败:', error)
  }
}

// 更新配置
const updateConfig = (key: string, value: any) => {
  emit('update:modelConfig', {
    ...props.modelConfig,
    [key]: value,
  })
}

onMounted(() => {
  fetchModelList()
})
</script>

<!-- resolved 只留下模型、随机性、携带上下文轮数、开启单次回复限制、最大回复长度 -->
<template>
  <div class="model-settings-form">
    <!-- 模型 -->
    <div class="form-item">
      <div class="form-left">
        <div class="form-label">
          <span>模型</span>
        </div>
      </div>
      <div class="form-right">
        <!-- modelOptions-{{ modelOptions }} -->
        <Selector
          v-model="localConfig.modelId"
          :opentionList="modelOptions"
          :width="280"
          clearable
          placeholder="系统默认模型"
          @change="updateConfig('modelId', $event)"
        >
        </Selector>
      </div>
    </div>
    <el-divider style="margin: 20px 0" />
    <!-- 随机性 -->
    <div class="form-item">
      <div class="form-left">
        <div class="form-label">
          <span>随机性</span>
          <el-tooltip>
            <template #content>
              <div class="tooltip-content">
                <p>数值越大，回答越有创意和想象力；数值越小，回答越严谨。默认值1.0。</p>
                <p>场景建议：</p>
                <p>代码生成/数学解题：0.0</p>
                <p>数据抽取/分析：1.0</p>
                <p>通用对话：1.3</p>
                <p>翻译：1.3</p>
                <p>创意类写作/诗歌创作：1.5</p>
              </div>
            </template>
            <i class="iconfont icon-tishi cursor-pointer ml-2" />
          </el-tooltip>
        </div>
      </div>
      <div class="form-right">
        <el-slider
          v-model="localConfig.temperature"
          :min="0"
          :max="2"
          :step="0.1"
          show-input
          :disabled="disabled"
        />
      </div>
    </div>
    <el-divider style="margin: 20px 0" />
    <!-- 携带上下文轮数 -->
    <div class="form-item">
      <div class="form-left">
        <div class="form-label">
          <span>携带上下文轮数</span>
          <el-tooltip
            content="控制模型输出的 Tokens 长度上限。通常 100 Tokens 约等于 150 个中文汉字。"
          >
            <i class="iconfont icon-tishi cursor-pointer ml-2" />
          </el-tooltip>
        </div>
      </div>
      <div class="form-right">
        <!-- localConfig.maxContextCount-{{ localConfig.maxContextCount }} -->
        <!-- <el-input v-model="localConfig.maxContextCount" /> -->
        <el-slider
          v-model="localConfig.maxContextCount"
          :min="1"
          :max="20"
          :step="1"
          show-input
          :disabled="disabled"
        />
      </div>
    </div>
    <el-divider style="margin: 20px 0" />
    <!-- 开启单次回复限制 -->
    <div class="form-item">
      <div class="form-left">
        <div class="form-label">
          <span>开启单次回复限制</span>
        </div>
      </div>
      <div class="form-right flex justify-end">
        <el-switch v-model="localConfig.singleReplyLimitFlag" :disabled="disabled" />
      </div>
    </div>
    <!-- 最大回复长度 -->
    <div v-if="localConfig.singleReplyLimitFlag" class="form-item">
      <div class="form-left">
        <div class="form-label">
          <span>最大回复长度</span>
          <el-tooltip
            content="设置带入模型上下文的对话历史轮数。轮数越多，多轮对话的相关性越高，但消耗的 Token 也越多。"
          >
            <i class="iconfont icon-tishi cursor-pointer ml-2" />
          </el-tooltip>
        </div>
      </div>
      <div class="form-right">
        <el-slider
          v-model="localConfig.maxTokens"
          :min="0"
          :max="8000"
          :step="100"
          show-input
          :disabled="disabled"
        />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.model-settings-form {
  margin-top: 20px;
  :deep(.el-slider__input) {
    width: 70px;
    .el-input-number__decrease {
      display: none;
    }
    .el-input-number__increase {
      display: none;
    }
    .el-input__wrapper {
      padding-left: 0;
      padding-right: 0;
    }
  }
  :deep(.el-slider__runway) {
    background-color: #e4e7ed;
    height: 4px;
  }
  :deep(.el-slider__bar) {
    background-color: #9da0a6;
    height: 4px;
  }
  :deep(.el-slider__button) {
    width: 10px;
    height: 10px;
    background-color: #ffffff;
    border: solid 2px #9da0a6;
  }
  :deep(.el-slider__button-wrapper) {
    top: -9px;
    height: 16px;
  }
  .form-item {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .form-left {
      .form-label {
        font-size: 14px;
        color: #30343a;
        font-weight: 500;
        display: flex;
        align-items: center;
      }
      .form-instruction {
        font-size: 12px;
        color: #969ba4;
        line-height: 17px;
      }
    }
    .form-right {
      width: 280px;
    }
  }
}
</style>
