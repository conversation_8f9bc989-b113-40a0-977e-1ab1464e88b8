import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { TokenUsageTimeSeriesQueryBo, TokenUsageAggregatePageQueryBo } from '@/api/usage/types'
import {
  fireEvent, render, screen, cleanup, waitFor
} from '@testing-library/vue'
import { postBizTokenUsageTimeSeries, postBizTokenUsageAggregatePage } from '@/api/usage'
import Usage from '@/views/settings/usage/index.vue'
import { formatToken } from '@/utils/formatter'
import dayjs from 'dayjs'
import { mount } from '@vue/test-utils'
import { ElDatePicker } from 'element-plus'

// Mock the API functions
const {
  mockPostBizTokenUsageTimeSeries,
  mockPostBizTokenUsageAggregatePage,
  mockGetBizAiModelAvailableModels,
  // mockGetDeptTreeSelect,
  mockGetDeptUserList,
  mockQueryOutward,
  mockGetAiPlatformList,
  mockGetBizAgentAgentStoreList,
  mockListDept,
} = vi.hoisted(() => {
  return {
    mockPostBizTokenUsageTimeSeries: vi.fn(),
    mockPostBizTokenUsageAggregatePage: vi.fn(),
    mockGetBizAiModelAvailableModels: vi.fn(),
    // mockGetDeptTreeSelect: vi.fn(),
    mockGetDeptUserList: vi.fn(),
    mockQueryOutward: vi.fn(),
    mockGetAiPlatformList: vi.fn(),
    mockGetBizAgentAgentStoreList: vi.fn(),
    mockListDept: vi.fn(),
  }
})

// Mock the usage API module
vi.mock('@/api/usage', () => {
  return {
    postBizTokenUsageTimeSeries: mockPostBizTokenUsageTimeSeries,
    postBizTokenUsageAggregatePage: mockPostBizTokenUsageAggregatePage,
    // getDeptTreeSelect: mockGetDeptTreeSelect,
    getDeptUserList: mockGetDeptUserList,
  }
})

// Mock the model API module
vi.mock('@/api', () => {
  return {
    getBizAiModelAvailableModels: mockGetBizAiModelAvailableModels
  }
})

vi.mock('@/api/external-release', () => {
  return {
    queryOutward: mockQueryOutward
  }
})

vi.mock('@/api/model-base', () => {
  return {
    getAiPlatformList: mockGetAiPlatformList
  }
})

vi.mock('@/api/agent', () => {
  return {
    getBizAgentAgentStoreList: mockGetBizAgentAgentStoreList,
  }
})

vi.mock('@/api/system/dept', () => {
  return {
    listDept: mockListDept,
  }
})

// 在导入语句之后，测试描述之前添加
vi.mock('@/views/settings/usage/components/UsageChart.vue', () => ({
  default: {
    props: ['xAxisData', 'seriesData'],
    template: '<div data-testid="usage-chart-mock"></div>'
  }
}))

// 模拟 ModelInvocationDialog.vue 组件
vi.mock('@/views/settings/usage/components/ModelInvocationDialog/index.vue', () => ({
  default: {
    template: '<div data-testid="model-invocation-dialog-mock"></div>'
  }
}))

// 模拟 MemberInvocationDialog.vue 组件
vi.mock('@/views/settings/usage/components/MemberInvocationDialog.vue', () => ({
  default: {
    template: '<div data-testid="member-invocation-dialog-mock"></div>'
  }
}))

// 模拟 OutwardInvocationDialog.vue 组件
vi.mock('@/views/settings/usage/components/OutwardInvocationDialog.vue', () => ({
  default: {
    template: '<div data-testid="outward-invocation-dialog-mock"></div>'
  }
}))

/**
 * 获取最近 15 天的时间
 */
function getLast15Days() {
  const endTime = dayjs().format('YYYY-MM-DD 23:59:59')
  const startTime = dayjs().subtract(15, 'day').format('YYYY-MM-DD 00:00:00')

  return { startTime, endTime }
}

// 在文件顶部定义mock数据生成器
const generateMockData = {
  model: Array.from({ length: 25 }, (_, i) => ({
    entityId: i + 1,
    entityName: `模型${i + 1}`,
    groupName: `平台${Math.floor(i / 5) + 1}`,
    totalTokens: 1000 + i * 100,
    inputTokens: 400 + i * 50,
    outputTokens: 600 + i * 50,
    chatCount: 10 + i,
    totalCost: 0.5 + i * 0.1
  })),

  user: Array.from({ length: 30 }, (_, i) => ({
    entityId: i + 1,
    entityName: `用户${i + 1}`,
    groupName: `部门${Math.floor(i / 10) + 1}`,
    totalTokens: 800 + i * 80,
    inputTokens: 300 + i * 40,
    outputTokens: 500 + i * 40,
    chatCount: 5 + i,
    totalCost: 0.4 + i * 0.05
  })),

  outward: Array.from({ length: 15 }, (_, i) => ({
    entityId: i + 1,
    entityName: `应用${i + 1}`,
    groupName: `关联助手${Math.floor(i / 5) + 1}`,
    totalTokens: 1200 + i * 120,
    inputTokens: 500 + i * 60,
    outputTokens: 700 + i * 60,
    chatCount: 15 + i,
    totalCost: 0.6 + i * 0.15
  }))
};


describe('Usage - 用量统计', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks()

    // Mock the API responses
    mockPostBizTokenUsageTimeSeries.mockResolvedValue({
      code: 200,
      data: {
        dataPoints: [],
        summary: {
          totalTokens: 1000,
          inputTokens: 400,
          outputTokens: 600,
          chatCount: 10,
          totalCost: 0.5
        }
      }
    })

    mockPostBizTokenUsageAggregatePage.mockImplementation((params) => {
      const data = params.aggregateType === 'USER' ? generateMockData.user :
        params.aggregateType === 'OUTWARD' ? generateMockData.outward :
          generateMockData.model;

      const pageNum = params.pageNum || 1;
      const pageSize = params.pageSize || 10;
      const start = (pageNum - 1) * pageSize;
      return Promise.resolve({
        code: 200,
        rows: data.slice(start, start + pageSize),
        total: data.length
      });
    })

    mockGetBizAiModelAvailableModels.mockResolvedValue({
      code: 200,
      data: [
        {
          "deptId": 100,
          "parentId": 0,
          "parentName": null,
          "ancestors": "0",
          "deptName": "川建国",
          "deptCategory": null,
          "orderNum": 1,
          "leader": null,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": "1926927677828440065",
          "parentId": 100,
          "parentName": null,
          "ancestors": "0,100",
          "deptName": "土星移民局",
          "deptCategory": null,
          "orderNum": 1,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-05-26 17:05:34"
        },
        {
          "deptId": "1915220480626888706",
          "parentId": 100,
          "parentName": null,
          "ancestors": "0,100",
          "deptName": "嘉兴分公司111",
          "deptCategory": null,
          "orderNum": 2,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-04-24 09:45:20"
        },
        {
          "deptId": 101,
          "parentId": 100,
          "parentName": null,
          "ancestors": "0,100",
          "deptName": "深圳总公司",
          "deptCategory": null,
          "orderNum": 3,
          "leader": null,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": 102,
          "parentId": 100,
          "parentName": null,
          "ancestors": "0,100",
          "deptName": "长沙分公司",
          "deptCategory": null,
          "orderNum": 4,
          "leader": null,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": 104,
          "parentId": 101,
          "parentName": null,
          "ancestors": "0,100,101",
          "deptName": "市场部门1",
          "deptCategory": null,
          "orderNum": 1,
          "leader": null,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": "1915276688620621826",
          "parentId": 101,
          "parentName": null,
          "ancestors": "0,100,101",
          "deptName": "秀洲区222222",
          "deptCategory": null,
          "orderNum": 2,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-04-24 13:28:41"
        },
        {
          "deptId": 105,
          "parentId": 101,
          "parentName": null,
          "ancestors": "0,100,101",
          "deptName": "测试部门",
          "deptCategory": null,
          "orderNum": 3,
          "leader": null,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": 103,
          "parentId": 101,
          "parentName": null,
          "ancestors": "0,100,101",
          "deptName": "研发部门",
          "deptCategory": null,
          "orderNum": 4,
          "leader": 1,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": 106,
          "parentId": 101,
          "parentName": null,
          "ancestors": "0,100,101",
          "deptName": "财务部门",
          "deptCategory": null,
          "orderNum": 5,
          "leader": null,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": 107,
          "parentId": 101,
          "parentName": null,
          "ancestors": "0,100,101",
          "deptName": "运维部门",
          "deptCategory": null,
          "orderNum": 6,
          "leader": null,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": "1915294937009721346",
          "parentId": "1915276688620621826",
          "parentName": null,
          "ancestors": "0,100,101,1915276688620621826",
          "deptName": "33",
          "deptCategory": null,
          "orderNum": 1,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-04-24 14:41:12"
        },
        {
          "deptId": 108,
          "parentId": 102,
          "parentName": null,
          "ancestors": "0,100,102",
          "deptName": "市场部门",
          "deptCategory": null,
          "orderNum": 1,
          "leader": null,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": 109,
          "parentId": 102,
          "parentName": null,
          "ancestors": "0,100,102",
          "deptName": "财务部门",
          "deptCategory": null,
          "orderNum": 2,
          "leader": null,
          "leaderName": null,
          "phone": "15888888888",
          "email": "<EMAIL>",
          "status": "0",
          "createTime": "2024-09-09 09:53:28"
        },
        {
          "deptId": "1933443984841211906",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "test1",
          "deptCategory": null,
          "orderNum": 1,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-13 16:39:02"
        },
        {
          "deptId": "1933444123823669249",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "test111",
          "deptCategory": null,
          "orderNum": 2,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-13 16:39:35"
        },
        {
          "deptId": "1938420221036797954",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "test14",
          "deptCategory": null,
          "orderNum": 3,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-27 10:12:49"
        },
        {
          "deptId": "1933444151300554753",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "test1111",
          "deptCategory": null,
          "orderNum": 4,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-13 16:39:42"
        },
        {
          "deptId": "1933444094782308354",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "test11",
          "deptCategory": null,
          "orderNum": 5,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-13 16:39:29"
        },
        {
          "deptId": "1934447673244721154",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "test133",
          "deptCategory": null,
          "orderNum": 6,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-16 11:07:20"
        },
        {
          "deptId": "1933444174159511554",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "test11111",
          "deptCategory": null,
          "orderNum": 7,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-13 16:39:47"
        },
        {
          "deptId": "1933444198998179842",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "test12",
          "deptCategory": null,
          "orderNum": 8,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-13 16:39:53"
        },
        {
          "deptId": "1933444222758912001",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "test122",
          "deptCategory": null,
          "orderNum": 9,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-13 16:39:59"
        },
        {
          "deptId": "1934447598841962497",
          "parentId": "1915220480626888706",
          "parentName": null,
          "ancestors": "0,100,1915220480626888706",
          "deptName": "tes13",
          "deptCategory": null,
          "orderNum": 10,
          "leader": null,
          "leaderName": null,
          "phone": null,
          "email": null,
          "status": "0",
          "createTime": "2025-06-16 11:07:03"
        }
      ]
    })

    mockListDept.mockResolvedValue({
      code: 200,
      data: [
        {
          deptId: 1,
          deptName: '部门1'
        }
      ]
    })

    mockGetDeptUserList.mockResolvedValue({
      code: 200,
      data: [
        {
          userId: 1,
          nickName: '用户1'
        }
      ]
    })

    mockQueryOutward.mockResolvedValue({
      code: 200,
      rows: [
        {
          id: 1,
          name: '应用1',
          outwardType: { code: 'API' },
          enabled: true,
          tokens: 1000,
          maxTokens: 5000
        }
      ],
      total: 1
    })

    mockGetAiPlatformList.mockResolvedValue({
      code: 200,
      rows: [
        {
          id: '1',
          platformName: '平台1',
          platformCode: 'platform1',
          isEnabled: true,
          description: '平台1描述',
          icon: 'icon-platform1',
          baseUrl: 'https://api.platform1.com',
          apiKey: 'api-key-1'
        }
      ],
      total: 1
    })

    mockGetBizAgentAgentStoreList.mockResolvedValue({
      code: 200,
      rows: [
        {
          backgroundColor: "#f0f0f0",
          categoryId: "category123",
          categoryIdList: ["category123", "category456"],
          description: "这是一个示例助手，用于演示 AgentVo 接口的数据结构。",
          emoji: "🤖",
          id: "agent789",
          isPrivate: false,
          modelId: "model123",
          name: "示例助手",
          prompt: "你是一个有用的助手。",
          status: 1
        }
      ]
    })
  })

  afterEach(() => {
    cleanup()
    // Clean up after each test
    vi.restoreAllMocks()
  })

  it('切换 模型统计、成员统计、应用统计 tab时，表格接口重新请求数据', async () => {
    render(Usage)
    // 页面初始化加载一次
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledTimes(1)
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledTimes(1)
    await fireEvent.click(screen.getByText('成员统计'))
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledTimes(2)
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledTimes(2)
    await fireEvent.click(screen.getByText('应用统计'))
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledTimes(3)
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledTimes(3)
    await fireEvent.click(screen.getByText('模型统计'))
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledTimes(4)
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledTimes(4)
  })

  it('页面初始化查询，接口调用参数是否正确', async () => {
    // 渲染 ModelInvocationDialog 组件
    render(Usage)

    const { startTime, endTime } = getLast15Days()

    // 模型统计 - 模型统计表格数据 日期为最近 15天 粒度是 DAY， 类型为 MODEL
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledWith({
      startTime,
      endTime,
      aggregateType: 'MODEL', // 默认是第一个 tab，对应 MODEL
      pageNum: 1,
      pageSize: 10,
    })

    // 时间序列统计 - 时间序列统计表格数据 日期为最近 15天 粒度是 DAY
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledWith({
      startTime,
      endTime,
      granularity: 'DAY',
      statType: 'MODEL',
    })
  })

  it('成员统计、应用统计，切换以后，表格接口请求参数 是否正确，应该是第一页，类型应该改变', async () => {
    render(Usage)
    await fireEvent.click(screen.getByText('成员统计'))
    const { startTime, endTime } = getLast15Days()

    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledWith({
      startTime,
      endTime,
      aggregateType: 'USER',
      pageNum: 1,
      pageSize: 10,
    })
    await fireEvent.click(screen.getByText('应用统计'))
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledWith({
      startTime,
      endTime,
      aggregateType: 'OUTWARD',
      pageNum: 1,
      pageSize: 10,
    })
  })

  it('成员统计 - 应该存在 选择部门 和 选择人员', async () => {
    render(Usage)
    await fireEvent.click(screen.getByText('成员统计'))
    expect(screen.getByText('选择部门')).toBeInTheDocument()
    expect(screen.getByText('选择人员')).toBeInTheDocument()
  })

  it('应用统计 - 应该存在 选择应用', async () => {
    render(Usage)
    await fireEvent.click(screen.getByText('应用统计'))
    expect(screen.getByText('选择应用')).toBeInTheDocument()
  })

  it('切换查询颗粒度为 HOUR, 时间统计接口应该被调用', async () => {
    render(Usage)

    await fireEvent.click(screen.getByText('小时'))
    const { startTime, endTime } = getLast15Days()

    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledWith({
      startTime,
      endTime,
      granularity: 'HOUR',
      statType: 'MODEL'
    })

    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledTimes(2)
  })

  it('切换展示Token统计聚合粒度，时间统计接口应该被调用获取最新的数据', async () => {
    render(Usage)
    const tokensInput = screen.getByRole('tab', { name: /输入Tokens数/ })
    await fireEvent.click(tokensInput)
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledTimes(2)
    expect(tokensInput).toHaveClass('active')
  })

  it('切换成成员统计，表格列表改变', async () => {
    const { debug } = render(Usage, {
      global: {
        stubs: {
          transition: false
        }
      }
    })
    await waitFor(() => {
      expect(screen.getByText('模型1')).toBeTruthy()
    })
    // debug(undefined, 100000)
    // 验证初始状态（模型统计模式）下的表格列
    expect(screen.getByText('序号')).toBeInTheDocument()
    expect(screen.getByText('模型')).toBeInTheDocument()
    expect(screen.getByText('平台')).toBeInTheDocument()
    expect(screen.getByRole('columnheader', { name: '输入Tokens数' })).toBeInTheDocument()
    expect(screen.getByRole('columnheader', { name: '输出Tokens数' })).toBeInTheDocument()
    expect(screen.getByRole('columnheader', { name: '调用总量Tokens数' })).toBeInTheDocument()
    expect(screen.getByText('操作')).toBeInTheDocument()

    // 切换成成员统计模式
    await fireEvent.click(screen.getByText('成员统计'))
    await waitFor(() => {
      // 等待动画和数据加载完成
      expect(screen.getByText('用户1')).toBeTruthy()
    })

    // 验证成员统计模式下的表格列
    expect(screen.getByText('序号')).toBeInTheDocument()
    expect(screen.getByText('姓名')).toBeInTheDocument()
    expect(screen.getByText('部门')).toBeInTheDocument()
    expect(screen.getByRole('columnheader', { name: '输入Tokens数' })).toBeInTheDocument()
    expect(screen.getByRole('columnheader', { name: '输出Tokens数' })).toBeInTheDocument()
    expect(screen.getByRole('columnheader', { name: '调用总量Tokens数' })).toBeInTheDocument()
    expect(screen.getByText('操作')).toBeInTheDocument()

    // 确保模型统计模式的列不再存在
    expect(screen.queryByText('模型')).not.toBeInTheDocument()
    expect(screen.queryByText('平台')).not.toBeInTheDocument()

    // 切换成应用统计模式
    await fireEvent.click(screen.getByText('应用统计'))
    await waitFor(() => {
      expect(screen.getByText('应用2')).toBeTruthy()
    })

    // 验证应用统计模式下的表格列
    expect(screen.getByText('序号')).toBeInTheDocument()
    expect(screen.getByText('名称')).toBeInTheDocument()
    expect(screen.getByText('关联助手')).toBeInTheDocument()
    expect(screen.getByRole('columnheader', { name: '输入Tokens数' })).toBeInTheDocument()
    expect(screen.getByRole('columnheader', { name: '输出Tokens数' })).toBeInTheDocument()
    expect(screen.getByRole('columnheader', { name: '调用总量Tokens数' })).toBeInTheDocument()
    expect(screen.getByText('操作')).toBeInTheDocument()

    // 确保成员统计模式的列不再存在
    expect(screen.queryByText('姓名')).not.toBeInTheDocument()
    expect(screen.queryByText('部门')).not.toBeInTheDocument()
  })

  it('表格中token数显示', async () => {
    render(Usage)
    await waitFor(() => {
      expect(screen.getByText('模型1')).toBeTruthy()
    })
    expect(screen.getByText(formatToken(1100))).toBeInTheDocument()
  })

  // 新增测试用例来验证API调用是否被触发
  it('验证postBizTokenUsageTimeSeries API调用', async () => {
    // 直接调用函数来测试API是否被触发
    const params: TokenUsageTimeSeriesQueryBo = {
      startTime: '2023-01-01 00:00:00',
      endTime: '2023-01-02 23:59:59',
      granularity: 'DAY'
    }

    await postBizTokenUsageTimeSeries(params)

    // 验证API是否被调用
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledWith(params)
  })

  it('验证postBizTokenUsageAggregatePage API调用', async () => {
    // 直接调用函数来测试API是否被触发
    const params: TokenUsageAggregatePageQueryBo = {
      startTime: '2023-01-01 00:00:00',
      endTime: '2023-01-02 23:59:59',
      aggregateType: 'MODEL',
      sortBy: 'totalTokens',
      sortOrder: 'DESC',
      pageNum: 1,
      pageSize: 10
    }

    await postBizTokenUsageAggregatePage(params)

    // 验证API是否被调用
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledWith(params)
  })

  // 验证组件初始化时API调用
  it('组件初始化时调用两个主要API', async () => {
    // 创建测试参数
    const timeSeriesParams: TokenUsageTimeSeriesQueryBo = {
      startTime: '2023-01-01 00:00:00',
      endTime: '2023-01-15 23:59:59',
      granularity: 'DAY'
    }

    const aggregateParams: TokenUsageAggregatePageQueryBo = {
      startTime: '2023-01-01 00:00:00',
      endTime: '2023-01-15 23:59:59',
      aggregateType: 'MODEL',
      sortBy: 'totalTokens',
      sortOrder: 'DESC',
      pageNum: 1,
      pageSize: 10
    }

    // 调用API函数
    await postBizTokenUsageTimeSeries(timeSeriesParams)
    await postBizTokenUsageAggregatePage(aggregateParams)

    // 验证API调用
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledWith(timeSeriesParams)
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledWith(aggregateParams)

    // 验证调用次数
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledTimes(1)
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledTimes(1)
  })

  it('选择日期后，接口重新请求数据', async () => {
    // 如果使用 testing-library/vue，需要模拟用户的行为，测试写起来会非常麻烦。考虑到 组件的相关行为，组件库的作者已经做过想过的测试了。这里使用 vue-test-utils, 直接触发组件的 change 事件。
    vi.useFakeTimers()
    const wrapper = mount(Usage)
    const datePicker = wrapper.findComponent(ElDatePicker)
    expect(datePicker.exists()).toBe(true)
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledTimes(1)


    const dataRange = [dayjs().subtract(7, 'day').toDate(), dayjs().toDate()]
    datePicker.setValue(dataRange)
    datePicker.vm.$emit('change', dataRange)
    expect(mockPostBizTokenUsageTimeSeries).toHaveBeenCalledTimes(2)
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledTimes(2)
  })

  it('页码切换时，接口重新请求数据', async () => {
    render(Usage, {
      global: {
        stubs: {
          transition: false
        }
      }
    })
    await waitFor(() => {
      expect(screen.getByText('模型1')).toBeTruthy()
    })
    await fireEvent.click(screen.getByLabelText('page 2', { selector: 'li' }))
    expect(mockPostBizTokenUsageAggregatePage).toHaveBeenCalledTimes(2)
  })
})