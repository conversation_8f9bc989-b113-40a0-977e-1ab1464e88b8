<script setup lang="ts">
import { deleteOutward, disableOutward, enableOutward, queryOutward } from '@/api/external-release'
import { OutwardQueryParam, OutwardType, OutwardVo } from '@/api/external-release/type'
import { Sort } from 'element-plus'
import { formatToken } from '@/utils/formatter'
import dangerIcon from '@/assets/chat/error.svg'
import canUpdateIcon from '@/assets/settings/external-release/can-update-icon.png'
import DeadlineTag from './components/DeadlineTag.vue'
import PublicationTypeDialog from './components/PublicationTypeDialog.vue'
import AddReleaseDialog from './components/AddReleaseDialog.vue'
import ReleaseInfoDialog from './components/ReleaseInfoDialog.vue'
import UpdateAgentDialog from './components/UpdateAgentDialog.vue'
import { getOutwardIcon } from '@/utils/external-release'

defineOptions({
  name: 'ExternalRelease',
})

// 查询参数
const queryParams = ref<OutwardQueryParam>({
  name: '',
  pageNum: 1,
  pageSize: 10,
  orderByColumn: '',
  isAsc: 'asc',
})

const loading = ref(false)
const tableData = ref<OutwardVo[]>([])
const total = ref(0)

/**
 *
 */
function getOutwardList() {
  loading.value = true
  // 暂时注释掉真实的接口调用，以便样式修改
  queryOutward(queryParams.value)
    .then(res => {
      tableData.value = res.rows
      total.value = res.total
    })
    .finally(() => {
      loading.value = false
    })
}
onMounted(() => {
  getOutwardList()
})

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getOutwardList()
}
/**
 * 编辑时间排序
 */
function sortChange({ prop, order }: Sort) {
  let isAsc = ''
  if (order === 'ascending') {
    isAsc = 'asc'
  } else if (order === 'descending') {
    isAsc = 'desc'
  }
  queryParams.value.pageNum = 1
  queryParams.value.orderByColumn = prop
  queryParams.value.isAsc = isAsc
  getOutwardList()
}

/**
 * 删除发布
 */
const handleDelete = (id: string) => {
  ElMessageBox.confirm('确认删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    if (id !== undefined) {
      deleteOutward(id)
        .then(res => {
          if (res.code == 200) {
            ElMessage.success('删除成功')
            if (tableData.value.length === 1 && queryParams.value.pageNum > 1) {
              queryParams.value.pageNum -= 1
            }
            getOutwardList()
          } else {
            ElMessage.error(`删除失败: ${res.msg}`)
          }
        })
        .catch((error: any) => {
          ElMessage.error(`删除失败: ${error.message}`)
        })
    } else {
      ElMessage.error('发布ID未定义')
    }
  })
}

function handleEnable(id: string, enabled: boolean) {
  console.log('handleEnable', id, enabled)

  return enabled ? enableOutward(id) : disableOutward(id)
}

/**
 * 选择发布类型的弹窗显隐
 */
const publictionTypeVisible = ref(false)
/**
 * 新增助手
 */
const handleAdd = () => {
  publictionTypeVisible.value = true
}

/**
 * 新增发布弹窗显隐
 */
const addReleaseDialogRef = ref()
const addReleaseVisible = ref(false)
const curentType = ref<OutwardType>()
function handleSelectionChange(type: OutwardType) {
  addReleaseVisible.value = true
  curentType.value = type
}

/**
 * 编辑发布
 */
function handleEdit(outwardItem: OutwardVo) {
  addReleaseVisible.value = true
  if (addReleaseDialogRef.value) {
    addReleaseDialogRef.value.edit(outwardItem)
  }
}

const releaseInfoDialogRef = ref()
/**
 * 查看发布弹窗显隐
 */
const releaseInfoVisible = ref(false)
/**
 * 查看发布
 */
const handleLookUp = (id: number | string) => {
  releaseInfoVisible.value = true
  if (releaseInfoDialogRef.value) {
    releaseInfoDialogRef.value.show(id)
  }
}

// /**
//  * 处理行点击事件
//  */
// const handleRowClick = (row: OutwardVo, column: any, event: MouseEvent) => {
//   // console.log(row, column, event)
//   if (['启用', '操作'].includes(column.label)) {
//     return
//   }
//   handleLookUp(row.id!)
// }

const updateAgentDialogRef = ref()
/**
 * 更新助手
 * @param id
 */
function updateAgent(id: string) {
  if (updateAgentDialogRef.value) {
    updateAgentDialogRef.value.updateAgent(id)
  }
}

// function getCellclassName({ column }) {
//   if (['启用', '操作'].includes(column.label)) {
//     return ''
//   } else {
//     return 'cursor-pointer'
//   }
// }
</script>

<template>
  <div class="my-container">
    <div class="flex justify-between">
      <div class="flex">
        <el-button class="ex-el-button-gray" type="primary" plain @click="handleAdd()">
          <i class="iconfont iconfont-c icon-xinzeng mr-2"></i>
          新增
        </el-button>
      </div>
      <div class="flex justify-end">
        <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="text-right">
          <el-form-item label="" prop="name" class="!mr-0">
            <el-input
              v-model="queryParams.name"
              style="width: 400px"
              placeholder="请输入你需要搜索的内容"
              clearable
              @clear="handleQuery"
              @keyup.enter="handleQuery"
            >
              <template #suffix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
        <!-- <el-button class="ml-3" type="primary" plain @click="handleQuery"> 高级搜索 </el-button> -->
      </div>
    </div>

    <el-table v-loading="loading" :data="tableData" style="width: 100%" @sort-change="sortChange">
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="name" label="名称" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="cursor-pointer" @click="handleLookUp(row.id!)">
            <img
              class="size-8 inline-block mr-2"
              :src="getOutwardIcon(row.outwardType.code)"
              alt=""
            />
            <span class="text-[16px]/{32px}">
              {{ row.name }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="agentName"
        class-name="associate-assistant-cell"
        label="关联助手"
        show-overflow-tooltip
      >
        <!-- 如果 有助手更新，需要显示图标 padding-right 和 定位 -->
        <template #default="{ row }">
          <div class="flex items-center">
            <div
              class="emoji-wrap !w-[32px] !h-[32px] mr-2"
              :style="{ backgroundColor: row.agentBackgroundColor }"
            >
              <img v-if="row.agentEmoji" :src="row.agentEmoji" class="size-5" />
            </div>
            <div
              class="agent-name-wrap truncate"
              :style="{ paddingRight: row.agentIsNew ? '32px' : '' }"
            >
              <span class="text-[16px]/[32px]">
                {{ row.agentName }}
              </span>

              <el-tooltip
                v-if="!row.agentIsNew"
                content="发现助手有更新，是否同步？"
                placement="bottom-start"
              >
                <img
                  class="size-6 inline-block cursor-pointer absolute right-0 top-1"
                  :src="canUpdateIcon"
                  alt=""
                  @click.stop="updateAgent(row.id)"
                />
              </el-tooltip>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="deadline"
        label="截止有效期"
        width="216"
        sortable
        class-name="deadline-column"
        :sort-orders="['ascending', 'descending']"
      >
        <template #default="{ row }">
          <span>{{ row.deadline }}</span>
          <DeadlineTag class="ml-2" :deadline="row.deadline" />
        </template>
      </el-table-column>
      <el-table-column prop="tokens" label="累计模型消耗" width="150">
        <template #default="{ row }">
          <span>{{ `${formatToken(row.tokens)}` }}</span>
          <el-tooltip
            v-if="row.maxTokens !== -1 && row.tokens >= row.maxTokens"
            content="消耗已达上限！"
            placement="bottom-start"
          >
            <img class="size-4 inline-block ml-2 mt-[-3px]" :src="dangerIcon" alt="" />
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" show-overflow-tooltip> </el-table-column>
      <el-table-column prop="createByName" width="120" label="创建者"> </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="180"
        sortable
        :sort-orders="['ascending', 'descending']"
      />

      <el-table-column prop="enabled" label="启用" width="90" fixed="right">
        <template #default="{ row }">
          <EnableSwitch
            v-model="row.enabled"
            :handle-func-promise="() => handleEnable(row.id, row.enabled)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleLookUp(row.id)"> 查看 </el-button>
          <!-- <el-button link type="default" @click="handleEdit(row)"> 编辑 </el-button>
          <el-button link type="danger" @click="handleDelete(row)"> 删除 </el-button> -->
          <el-popover placement="bottom" :width="70" popper-style="min-width: 0;padding: 5px;">
            <template #reference>
              <el-icon class="align-sub ml-2"><More /></el-icon>
            </template>
            <div class="text-center hover-effect">
              <el-button link type="default" @click="handleEdit(row)"> 编辑 </el-button>
            </div>
            <div class="text-center hover-effect">
              <el-button link type="danger" @click="handleDelete(row.id)"> 删除 </el-button>
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="getOutwardList"
    />
  </div>
  <PublicationTypeDialog
    v-model="publictionTypeVisible"
    @selection-change="handleSelectionChange"
  />
  <AddReleaseDialog
    ref="addReleaseDialogRef"
    :outwardType="curentType"
    v-model="addReleaseVisible"
    @updateReleaseList="getOutwardList"
  />
  <ReleaseInfoDialog ref="releaseInfoDialogRef" v-model="releaseInfoVisible" />
  <UpdateAgentDialog ref="updateAgentDialogRef" @updateReleaseList="getOutwardList" />
</template>

<style scoped lang="less">
.my-container {
  padding: 20px;
}
.hover-effect {
  transition: background-color 0.3s ease;
  border-radius: 4px;
}

.hover-effect:hover {
  background-color: #ecf5ff; /* Light blue background for default/primary hover */
}
</style>
<style lang="less">
.associate-assistant-cell .cell {
  .agent-name-wrap {
    min-width: 40px;
    position: relative;
    padding-right: 28px; /* Adjusted for the update icon */
    display: inline-block;
    max-width: calc(100% - 32px); /* Adjusted for the emoji */
  }
}
.deadline-column .cell {
  padding-right: 8px;
}
</style>
