{"name": "ease-ai", "version": "2.11.1", "private": false, "packageManager": "pnpm@9.15.0+sha1.8bfdb6d72b4d5fdf87d21d27f2bfbe2b21dd2629", "description": "Ease AI", "author": "", "keywords": [], "main": "electron/main.js", "scripts": {"dev": "vite", "dev:mobile": "vite --mode mobile", "build": "npm run update-build-info && vite build", "build:stage": "npm run update-build-info && vite build --mode stage", "build:mobile": "npm run update-build-info && vite build --mode mobile", "update-build-info": "node -e \"const fs = require('fs'); const buildInfo = { buildTime: new Date().toISOString(), description: 'Build information for version checking' }; fs.writeFileSync('./public/build-info.json', JSON.stringify(buildInfo, null, 2));\"", "build-only": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix", "bootstrap": "pnpm install && pnpm run common:prepare", "common:cleanup": "rimraf node_modules && rimraf pnpm-lock.yaml", "common:prepare": "husky install", "electron:dev": "concurrently \"vite dev\" \"electron .\"", "electron:build": "vite build && electron-builder", "format": "prettier --write \"src/**/*.{js,ts,vue,less,css,scss}\"", "format:check": "prettier --check \"src/**/*.{js,ts,vue,less,css,scss}\"", "get-emoji-list": "node scripts/get-emoji-list.js", "download-emojis": "node scripts/download-emojis.js", "update": "echo y | deploy-cli-service deploy --mode dev", "test": "vitest", "coverage": "vitest run --coverage", "build:ci": "cross-env ELECTRON_SKIP_BINARY_DOWNLOAD=1 npm install --ignore-scripts && npm run build:stage"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@formkit/auto-animate": "^0.8.2", "@vscode/markdown-it-katex": "^1.0.3", "@vueuse/core": "^9.13.0", "await-to-js": "^3.0.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.3", "highlight.js": "^11.7.0", "html-to-image": "^1.11.11", "image-conversion": "^2.1.1", "jsencrypt": "^3.3.2", "katex": "^0.16.4", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "markdown-it": "^13.0.1", "markdown-it-footnote": "^4.0.0", "md-editor-v3": "^5.3.2", "mermaid-it-markdown": "^1.0.8", "naive-ui": "^2.34.3", "pinia": "^2.0.33", "vite-svg-loader": "^5.1.0", "vue": "^3.2.47", "vue-cropper": "^1.1.1", "vue-draggable-plus": "^0.6.0", "vue-echarts": "^7.0.3", "vue-i18n": "^9.2.2", "vue-router": "^4.1.6", "vue-types": "^5.1.3", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-emoji-picker": "^1.1.8"}, "devDependencies": {"@antfu/eslint-config": "^0.35.3", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@iconify/vue": "^4.1.0", "@playwright/test": "^1.52.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/vue": "^8.1.0", "@types/crypto-js": "^4.1.1", "@types/katex": "^0.16.0", "@types/lodash": "^4.17.20", "@types/markdown-it": "^12.2.3", "@types/markdown-it-footnote": "^3.0.4", "@types/markdown-it-link-attributes": "^3.0.1", "@types/node": "^18.14.6", "@vitejs/plugin-vue": "^4.0.0", "@vitest/coverage-v8": "3.2.4", "autoprefixer": "^10.4.13", "axios": "^1.3.4", "concurrently": "^8.2.2", "crypto-js": "^4.1.1", "dotenv": "^16.4.7", "electron": "^28.2.0", "electron-builder": "22.14.13", "eslint": "^8.35.0", "eslint-config-prettier": "^10.0.2", "husky": "^8.0.3", "jsdom": "^26.1.0", "less": "^4.1.3", "lint-staged": "^13.1.2", "markdown-it-link-attributes": "^4.0.1", "npm-run-all": "^4.1.5", "playwright": "^1.52.0", "postcss": "^8.4.21", "prettier": "^3.5.3", "rimraf": "^4.3.0", "tailwindcss": "^3.2.7", "typescript": "~5.8.3", "unplugin-auto-import": "0.16.7", "unplugin-vue-components": "0.25.2", "vite": "^4.2.0", "vite-plugin-pwa": "^0.14.4", "vitest": "^3.2.4", "vue-tsc": "^3.0.5"}, "lint-staged": {"*.{ts,tsx,vue}": ["pnpm lint:fix"]}, "build": {"appId": "com.chatgpt.web", "productName": "Ease AI Desktop", "directories": {"output": "dist_electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": ".env", "to": ".env"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "public/electron-desk.png"}}}