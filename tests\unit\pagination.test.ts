import { describe, it, expect } from 'vitest'
import { recalcPage } from '@/utils/pagination'

describe('recalcPage', () => {
  // 正常情况测试
  it('应正确计算新页码 - 每页数量增加', () => {
    expect(recalcPage(2, 10, 20)).toBe(1)
    expect(recalcPage(3, 10, 30)).toBe(1)
    expect(recalcPage(5, 10, 25)).toBe(2)
  })

  it('应正确计算新页码 - 每页数量减少', () => {
    expect(recalcPage(1, 20, 10)).toBe(1)
    expect(recalcPage(1, 30, 10)).toBe(1)
    expect(recalcPage(2, 20, 10)).toBe(3)
  })

  // 边界值测试
  it('应正确处理边界值', () => {
    // 第一页
    expect(recalcPage(1, 10, 5)).toBe(1)
    expect(recalcPage(1, 10, 20)).toBe(1)

    // 刚好跨页的情况
    expect(recalcPage(2, 10, 15)).toBe(1)
    expect(recalcPage(3, 10, 15)).toBe(2)
  })

  // 异常情况测试
  it('应抛出错误当参数不合法时', () => {
    expect(() => recalcPage(0, 10, 10)).toThrow('参数不合法')
    expect(() => recalcPage(1, 0, 10)).toThrow('参数不合法')
    expect(() => recalcPage(1, 10, 0)).toThrow('参数不合法')
    expect(() => recalcPage(-1, 10, 10)).toThrow('参数不合法')
  })
})