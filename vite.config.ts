/// <reference types="vitest" />
import path from 'path'
import fs from 'fs'
import type { PluginOption } from 'vite'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { VitePWA } from 'vite-plugin-pwa'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver, NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import AutoImport from 'unplugin-auto-import/vite'
import svgLoader from 'vite-svg-loader'

function setupPlugins(env: ImportMetaEnv): PluginOption[] {
  return [
    vue(),
    svgLoader({
      defaultImport: 'url', // or 'raw'
    }),
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        {
          'naive-ui': ['useDialog', 'useMessage', 'useNotification', 'useLoadingBar'],
        },
      ],
      resolvers: [ElementPlusResolver()],
      dts: 'src/auto-imports.d.ts',
    }),
    Components({
      resolvers: [ElementPlusResolver(), NaiveUiResolver()],
    }),
    env.VITE_GLOB_APP_PWA === 'true' &&
    VitePWA({
      injectRegister: 'auto',
      manifest: {
        name: 'Ease AI',
        short_name: 'Ease AI',
        icons: [
          { src: 'electron-256x256.png', sizes: '256x256', type: 'image/png' },
          // { src: 'pwa-192x192.png', sizes: '192x192', type: 'image/png' },
          // { src: 'pwa-512x512.png', sizes: '512x512', type: 'image/png' },
        ],
      },
    }),
  ]
}

export default defineConfig(env => {
  const viteEnv = loadEnv(env.mode, process.cwd()) as unknown as ImportMetaEnv
  console.log('viteEnv.VITE_APP_BASE_API_PROXY', viteEnv.VITE_APP_BASE_API_PROXY)

  // 从 build-info.json 读取构建时间戳，确保与服务器端一致
  let buildTime = new Date().toISOString() // 默认值
  try {
    const buildInfoPath = path.resolve(process.cwd(), 'public/build-info.json')
    if (fs.existsSync(buildInfoPath)) {
      const buildInfo = JSON.parse(fs.readFileSync(buildInfoPath, 'utf-8'))
      buildTime = buildInfo.buildTime || buildTime
      console.log('Using build time from build-info.json:', buildTime)
    } else {
      console.warn('build-info.json not found, using current timestamp:', buildTime)
    }
  } catch (error) {
    console.warn('Failed to read build-info.json, using current timestamp:', error)
  }

  return {
    define: {
      // 注入构建时间到环境变量
      'import.meta.env.VITE_BUILD_TIME': JSON.stringify(buildTime),
    },
    resolve: {
      alias: {
        '@': path.resolve(process.cwd(), 'src'),
      },
    },
    plugins: setupPlugins(viteEnv),
    base: './',
    server: {
      host: '0.0.0.0',
      port: 1002,
      open: false,
      proxy: {
        '/prod-api': {
          target: viteEnv.VITE_APP_BASE_API_PROXY,
          changeOrigin: true,
          rewrite: path => path.replace('/prod-api/', '/'),
          secure: false,
          ws: true,
        },
      },
    },
    build: {
      reportCompressedSize: false,
      sourcemap: false,
      commonjsOptions: {
        ignoreTryCatch: false,
      },
      outDir: 'dist',
      assetsDir: 'assets',
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
          },
        },
      },
    },
    test: {
      global: true,
      environment: 'jsdom',
      setupFiles: ['./tests/test-setup.ts'],
      include: ['tests/components/**/*.test.ts', 'tests/unit/**/*.test.ts'],
      server: {
        deps: {
          inline: ['element-plus'],
        },
      }
    },
  }
})
