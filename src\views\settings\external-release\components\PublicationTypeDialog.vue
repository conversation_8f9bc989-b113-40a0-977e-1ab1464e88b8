<script setup lang="ts">
import { OutwardType } from '@/api/external-release/type'
/**
 * 发布类型列表
 */
import { publicationTypeList } from '@/utils/external-release'

defineOptions({
  name: 'PublicationTypeDialog',
})

const emits = defineEmits(['selection-change'])

const dialogVisible = defineModel({ default: false })

function handleClose() {
  dialogVisible.value = false
}

/**
 * 选择发布类型
 */
const handleSelectPublicationType = (type: OutwardType) => {
  handleClose()
  emits('selection-change', type)
}
</script>

<template>
  <el-dialog v-model="dialogVisible" width="1000" @close="handleClose">
    <template #header>
      <span class="text-[24px] g-family-medium">选择发布类型</span>
    </template>
    <div class="type-content">
      <div v-for="item in publicationTypeList" class="type-item" :class="{ publish: item.publish }">
        <img
          class="type-icon"
          :src="item.icon"
          alt=""
          @click="item.publish && handleSelectPublicationType(item.key)"
        />
        <div class="type-name g-family-medium">{{ item.name }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="less" scoped>
.type-content {
  display: grid;
  grid-template-columns: repeat(4, min-content);
  justify-content: space-between;
  width: 80%;
  margin: 0 auto 40px;
  row-gap: 30px;
}
.type-item {
  text-align: center;
  width: 140px;
  .type-icon {
    width: 140px;
    height: 140px;
    transition: all 0.3s ease-in-out;
    border-radius: 16px;
  }
  .type-name {
    margin-top: 26px;
    font-size: 16px;
    cursor: default;
  }

  .publish&:hover {
    .type-icon {
      border: 2px solid #4865e8;
      transform: scale(1.07);
    }
    .type-name {
      color: #4865e8;
    }
  }
}
</style>
