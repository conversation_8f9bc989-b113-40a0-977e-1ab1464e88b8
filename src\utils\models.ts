import Ai360Mode<PERSON><PERSON>ogo from '@/assets/model-logos/360.png'
import Ai360ModelLogoDark from '@/assets/model-logos/360_dark.png'
import AdeptModelLogo from '@/assets/model-logos/adept.png'
import AdeptModelLogoDark from '@/assets/model-logos/adept_dark.png'
import Ai21<PERSON>odelLogo from '@/assets/model-logos/ai21.png'
import Ai21ModelLogoDark from '@/assets/model-logos/ai21_dark.png'
import AimassModelLogo from '@/assets/model-logos/aimass.png'
import AimassModelLogoDark from '@/assets/model-logos/aimass_dark.png'
import AisingaporeModelLogo from '@/assets/model-logos/aisingapore.png'
import AisingaporeModelLogoDark from '@/assets/model-logos/aisingapore_dark.png'
import BaichuanModelLogo from '@/assets/model-logos/baichuan.png'
import BaichuanModelLogoDark from '@/assets/model-logos/baichuan_dark.png'
import Bge<PERSON>odel<PERSON>ogo from '@/assets/model-logos/bge.webp'
import BigcodeModel<PERSON>ogo from '@/assets/model-logos/bigcode.webp'
import BigcodeModelLogoDark from '@/assets/model-logos/bigcode_dark.webp'
import ChatGLMModelLogo from '@/assets/model-logos/chatglm.png'
import ChatGLMModelLogoDark from '@/assets/model-logos/chatglm_dark.png'
import ChatGptModelLogo from '@/assets/model-logos/chatgpt.jpeg'
import ClaudeModelLogo from '@/assets/model-logos/claude.png'
import ClaudeModelLogoDark from '@/assets/model-logos/claude_dark.png'
import CodegeexModelLogo from '@/assets/model-logos/codegeex.png'
import CodegeexModelLogoDark from '@/assets/model-logos/codegeex_dark.png'
import CodestralModelLogo from '@/assets/model-logos/codestral.png'
import CohereModelLogo from '@/assets/model-logos/cohere.png'
import CohereModelLogoDark from '@/assets/model-logos/cohere_dark.png'
import CopilotModelLogo from '@/assets/model-logos/copilot.png'
import CopilotModelLogoDark from '@/assets/model-logos/copilot_dark.png'
import DalleModelLogo from '@/assets/model-logos/dalle.png'
import DalleModelLogoDark from '@/assets/model-logos/dalle_dark.png'
import DbrxModelLogo from '@/assets/model-logos/dbrx.png'
import DeepSeekModelLogo from '@/assets/model-logos/deepseek.png'
import DeepSeekModelLogoDark from '@/assets/model-logos/deepseek_dark.png'
import DianxinModelLogo from '@/assets/model-logos/dianxin.png'
import DianxinModelLogoDark from '@/assets/model-logos/dianxin_dark.png'
import DoubaoModelLogo from '@/assets/model-logos/doubao.png'
import DoubaoModelLogoDark from '@/assets/model-logos/doubao_dark.png'
import EmbeddingModelLogo from '@/assets/model-logos/embedding.png'
import EmbeddingModelLogoDark from '@/assets/model-logos/embedding.png'
import FlashaudioModelLogo from '@/assets/model-logos/flashaudio.png'
import FlashaudioModelLogoDark from '@/assets/model-logos/flashaudio_dark.png'
import FluxModelLogo from '@/assets/model-logos/flux.png'
import FluxModelLogoDark from '@/assets/model-logos/flux_dark.png'
import GeminiModelLogo from '@/assets/model-logos/gemini.png'
import GeminiModelLogoDark from '@/assets/model-logos/gemini_dark.png'
import GemmaModelLogo from '@/assets/model-logos/gemma.png'
import GemmaModelLogoDark from '@/assets/model-logos/gemma_dark.png'
import GoogleModelLogo from '@/assets/model-logos/google.png'
import GoogleModelLogoDark from '@/assets/model-logos/google.png'
import ChatGPT35ModelLogo from '@/assets/model-logos/gpt_3.5.png'
import ChatGPT4ModelLogo from '@/assets/model-logos/gpt_4.png'
import ChatGptModelLogoDakr from '@/assets/model-logos/gpt_dark.png'
import ChatGPT35ModelLogoDark from '@/assets/model-logos/gpt_dark.png'
import ChatGPT4ModelLogoDark from '@/assets/model-logos/gpt_dark.png'
import ChatGPTo1ModelLogoDark from '@/assets/model-logos/gpt_dark.png'
import ChatGPTo1ModelLogo from '@/assets/model-logos/gpt_o1.png'
import GrokModelLogo from '@/assets/model-logos/grok.png'
import GrokModelLogoDark from '@/assets/model-logos/grok_dark.png'
import GrypheModelLogo from '@/assets/model-logos/gryphe.png'
import GrypheModelLogoDark from '@/assets/model-logos/gryphe_dark.png'
import HailuoModelLogo from '@/assets/model-logos/hailuo.png'
import HailuoModelLogoDark from '@/assets/model-logos/hailuo_dark.png'
import HuggingfaceModelLogo from '@/assets/model-logos/huggingface.png'
import HuggingfaceModelLogoDark from '@/assets/model-logos/huggingface_dark.png'
import HunyuanModelLogo from '@/assets/model-logos/hunyuan.png'
import HunyuanModelLogoDark from '@/assets/model-logos/hunyuan_dark.png'
import IbmModelLogo from '@/assets/model-logos/ibm.png'
import IbmModelLogoDark from '@/assets/model-logos/ibm_dark.png'
import InternlmModelLogo from '@/assets/model-logos/internlm.png'
import InternlmModelLogoDark from '@/assets/model-logos/internlm_dark.png'
import InternvlModelLogo from '@/assets/model-logos/internvl.png'
import JinaModelLogo from '@/assets/model-logos/jina.png'
import JinaModelLogoDark from '@/assets/model-logos/jina_dark.png'
import KeLingModelLogo from '@/assets/model-logos/keling.png'
import KeLingModelLogoDark from '@/assets/model-logos/keling_dark.png'
import LlamaModelLogo from '@/assets/model-logos/llama.png'
import LlamaModelLogoDark from '@/assets/model-logos/llama_dark.png'
import LLavaModelLogo from '@/assets/model-logos/llava.png'
import LLavaModelLogoDark from '@/assets/model-logos/llava_dark.png'
import LumaModelLogo from '@/assets/model-logos/luma.png'
import LumaModelLogoDark from '@/assets/model-logos/luma_dark.png'
import MagicModelLogo from '@/assets/model-logos/magic.png'
import MagicModelLogoDark from '@/assets/model-logos/magic_dark.png'
import MediatekModelLogo from '@/assets/model-logos/mediatek.png'
import MediatekModelLogoDark from '@/assets/model-logos/mediatek_dark.png'
import MicrosoftModelLogo from '@/assets/model-logos/microsoft.png'
import MicrosoftModelLogoDark from '@/assets/model-logos/microsoft_dark.png'
import MidjourneyModelLogo from '@/assets/model-logos/midjourney.png'
import MidjourneyModelLogoDark from '@/assets/model-logos/midjourney_dark.png'
import MinicpmModelLogo from '@/assets/model-logos/minicpm.webp'
import MinicpmModelLogoDark from '@/assets/model-logos/minicpm.webp'
import MinimaxModelLogo from '@/assets/model-logos/minimax.png'
import MinimaxModelLogoDark from '@/assets/model-logos/minimax_dark.png'
import MistralModelLogo from '@/assets/model-logos/mixtral.png'
import MistralModelLogoDark from '@/assets/model-logos/mixtral_dark.png'
import MoonshotModelLogo from '@/assets/model-logos/moonshot.png'
import MoonshotModelLogoDark from '@/assets/model-logos/moonshot_dark.png'
import NousResearchModelLogo from '@/assets/model-logos/nousresearch.png'
import NousResearchModelLogoDark from '@/assets/model-logos/nousresearch.png'
import NvidiaModelLogo from '@/assets/model-logos/nvidia.png'
import NvidiaModelLogoDark from '@/assets/model-logos/nvidia_dark.png'
import PalmModelLogo from '@/assets/model-logos/palm.png'
import PalmModelLogoDark from '@/assets/model-logos/palm_dark.png'
import PerplexityModelLogo from '@/assets/model-logos/perplexity.png'
import PerplexityModelLogoDark from '@/assets/model-logos/perplexity.png'
import PixtralModelLogo from '@/assets/model-logos/pixtral.png'
import PixtralModelLogoDark from '@/assets/model-logos/pixtral_dark.png'
import QwenModelLogo from '@/assets/model-logos/qwen.png'
import QwenModelLogoDark from '@/assets/model-logos/qwen_dark.png'
import RakutenaiModelLogo from '@/assets/model-logos/rakutenai.png'
import RakutenaiModelLogoDark from '@/assets/model-logos/rakutenai_dark.png'
import SparkDeskModelLogo from '@/assets/model-logos/sparkdesk.png'
import SparkDeskModelLogoDark from '@/assets/model-logos/sparkdesk_dark.png'
import StabilityModelLogo from '@/assets/model-logos/stability.png'
import StabilityModelLogoDark from '@/assets/model-logos/stability_dark.png'
import StepModelLogo from '@/assets/model-logos/step.png'
import StepModelLogoDark from '@/assets/model-logos/step_dark.png'
import SunoModelLogo from '@/assets/model-logos/suno.png'
import SunoModelLogoDark from '@/assets/model-logos/suno_dark.png'
import TeleModelLogo from '@/assets/model-logos/tele.png'
import TeleModelLogoDark from '@/assets/model-logos/tele_dark.png'
import UpstageModelLogo from '@/assets/model-logos/upstage.png'
import UpstageModelLogoDark from '@/assets/model-logos/upstage_dark.png'
import ViduModelLogo from '@/assets/model-logos/vidu.png'
import ViduModelLogoDark from '@/assets/model-logos/vidu_dark.png'
import VoyageModelLogo from '@/assets/model-logos/voyageai.png'
import WenxinModelLogo from '@/assets/model-logos/wenxin.png'
import WenxinModelLogoDark from '@/assets/model-logos/wenxin_dark.png'
import XirangModelLogo from '@/assets/model-logos/xirang.png'
import XirangModelLogoDark from '@/assets/model-logos/xirang_dark.png'
import YiModelLogo from '@/assets/model-logos/yi.png'
import YiModelLogoDark from '@/assets/model-logos/yi_dark.png'

/**
 * 获取模型对应的图标
 * @param modelId 模型ID
 * @returns 模型图标路径或图标名称
 */
export function getModelLogo(modelId: string, defaultIcon: string = ChatGptModelLogo) {
  // 默认使用亮色图标
  const isLight = true
  // const defaultIcon = 'ri:ai-generate'

  if (!modelId) {
    return defaultIcon
  }

  try {
    // 使用映射表直接返回导入的图片
    const logoMap = {
      pixtral: isLight ? PixtralModelLogo : PixtralModelLogoDark,
      jina: isLight ? JinaModelLogo : JinaModelLogoDark,
      abab: isLight ? MinimaxModelLogo : MinimaxModelLogoDark,
      minimax: isLight ? MinimaxModelLogo : MinimaxModelLogoDark,
      o3: isLight ? ChatGPTo1ModelLogo : ChatGPTo1ModelLogoDark,
      o1: isLight ? ChatGPTo1ModelLogo : ChatGPTo1ModelLogoDark,
      'gpt-3': isLight ? ChatGPT35ModelLogo : ChatGPT35ModelLogoDark,
      'gpt-4': isLight ? ChatGPT4ModelLogo : ChatGPT4ModelLogoDark,
      gpts: isLight ? ChatGPT4ModelLogo : ChatGPT4ModelLogoDark,
      'text-moderation': isLight ? ChatGptModelLogo : ChatGptModelLogoDakr,
      'babbage-': isLight ? ChatGptModelLogo : ChatGptModelLogoDakr,
      'sora-': isLight ? ChatGptModelLogo : ChatGptModelLogoDakr,
      '(^|/)omni-': isLight ? ChatGptModelLogo : ChatGptModelLogoDakr,
      'Embedding-V1': isLight ? WenxinModelLogo : WenxinModelLogoDark,
      'text-embedding-v': isLight ? QwenModelLogo : QwenModelLogoDark,
      'text-embedding': isLight ? ChatGptModelLogo : ChatGptModelLogoDakr,
      'davinci-': isLight ? ChatGptModelLogo : ChatGptModelLogoDakr,
      glm: isLight ? ChatGLMModelLogo : ChatGLMModelLogoDark,
      deepseek: isLight ? DeepSeekModelLogo : DeepSeekModelLogoDark,
      '(qwen|qwq-|qvq-)': isLight ? QwenModelLogo : QwenModelLogoDark,
      gemma: isLight ? GemmaModelLogo : GemmaModelLogoDark,
      'yi-': isLight ? YiModelLogo : YiModelLogoDark,
      llama: isLight ? LlamaModelLogo : LlamaModelLogoDark,
      mixtral: isLight ? MistralModelLogo : MistralModelLogo,
      mistral: isLight ? MistralModelLogo : MistralModelLogoDark,
      codestral: CodestralModelLogo,
      ministral: isLight ? MistralModelLogo : MistralModelLogoDark,
      moonshot: isLight ? MoonshotModelLogo : MoonshotModelLogoDark,
      kimi: isLight ? MoonshotModelLogo : MoonshotModelLogoDark,
      phi: isLight ? MicrosoftModelLogo : MicrosoftModelLogoDark,
      baichuan: isLight ? BaichuanModelLogo : BaichuanModelLogoDark,
      claude: isLight ? ClaudeModelLogo : ClaudeModelLogoDark,
      gemini: isLight ? GeminiModelLogo : GeminiModelLogoDark,
      bison: isLight ? PalmModelLogo : PalmModelLogoDark,
      palm: isLight ? PalmModelLogo : PalmModelLogoDark,
      step: isLight ? StepModelLogo : StepModelLogoDark,
      hailuo: isLight ? HailuoModelLogo : HailuoModelLogoDark,
      doubao: isLight ? DoubaoModelLogo : DoubaoModelLogoDark,
      'ep-202': isLight ? DoubaoModelLogo : DoubaoModelLogoDark,
      cohere: isLight ? CohereModelLogo : CohereModelLogoDark,
      command: isLight ? CohereModelLogo : CohereModelLogoDark,
      minicpm: isLight ? MinicpmModelLogo : MinicpmModelLogoDark,
      '360': isLight ? Ai360ModelLogo : Ai360ModelLogoDark,
      aimass: isLight ? AimassModelLogo : AimassModelLogoDark,
      codegeex: isLight ? CodegeexModelLogo : CodegeexModelLogoDark,
      copilot: isLight ? CopilotModelLogo : CopilotModelLogoDark,
      creative: isLight ? CopilotModelLogo : CopilotModelLogoDark,
      balanced: isLight ? CopilotModelLogo : CopilotModelLogoDark,
      precise: isLight ? CopilotModelLogo : CopilotModelLogoDark,
      dalle: isLight ? DalleModelLogo : DalleModelLogoDark,
      'dall-e': isLight ? DalleModelLogo : DalleModelLogoDark,
      dbrx: isLight ? DbrxModelLogo : DbrxModelLogo,
      flashaudio: isLight ? FlashaudioModelLogo : FlashaudioModelLogoDark,
      flux: isLight ? FluxModelLogo : FluxModelLogoDark,
      grok: isLight ? GrokModelLogo : GrokModelLogoDark,
      hunyuan: isLight ? HunyuanModelLogo : HunyuanModelLogoDark,
      internlm: isLight ? InternlmModelLogo : InternlmModelLogoDark,
      internvl: InternvlModelLogo,
      llava: isLight ? LLavaModelLogo : LLavaModelLogoDark,
      magic: isLight ? MagicModelLogo : MagicModelLogoDark,
      midjourney: isLight ? MidjourneyModelLogo : MidjourneyModelLogoDark,
      'mj-': isLight ? MidjourneyModelLogo : MidjourneyModelLogoDark,
      'tao-': isLight ? WenxinModelLogo : WenxinModelLogoDark,
      'ernie-': isLight ? WenxinModelLogo : WenxinModelLogoDark,
      voice: isLight ? FlashaudioModelLogo : FlashaudioModelLogoDark,
      'tts-1': isLight ? ChatGptModelLogo : ChatGptModelLogoDakr,
      'whisper-': isLight ? ChatGptModelLogo : ChatGptModelLogoDakr,
      'stable-': isLight ? StabilityModelLogo : StabilityModelLogoDark,
      sd2: isLight ? StabilityModelLogo : StabilityModelLogoDark,
      sd3: isLight ? StabilityModelLogo : StabilityModelLogoDark,
      sdxl: isLight ? StabilityModelLogo : StabilityModelLogoDark,
      sparkdesk: isLight ? SparkDeskModelLogo : SparkDeskModelLogoDark,
      generalv: isLight ? SparkDeskModelLogo : SparkDeskModelLogoDark,
      wizardlm: isLight ? MicrosoftModelLogo : MicrosoftModelLogoDark,
      microsoft: isLight ? MicrosoftModelLogo : MicrosoftModelLogoDark,
      hermes: isLight ? NousResearchModelLogo : NousResearchModelLogoDark,
      gryphe: isLight ? GrypheModelLogo : GrypheModelLogoDark,
      suno: isLight ? SunoModelLogo : SunoModelLogoDark,
      chirp: isLight ? SunoModelLogo : SunoModelLogoDark,
      luma: isLight ? LumaModelLogo : LumaModelLogoDark,
      keling: isLight ? KeLingModelLogo : KeLingModelLogoDark,
      'vidu-': isLight ? ViduModelLogo : ViduModelLogoDark,
      ai21: isLight ? Ai21ModelLogo : Ai21ModelLogoDark,
      'jamba-': isLight ? Ai21ModelLogo : Ai21ModelLogoDark,
      mythomax: isLight ? GrypheModelLogo : GrypheModelLogoDark,
      nvidia: isLight ? NvidiaModelLogo : NvidiaModelLogoDark,
      dianxin: isLight ? DianxinModelLogo : DianxinModelLogoDark,
      tele: isLight ? TeleModelLogo : TeleModelLogoDark,
      adept: isLight ? AdeptModelLogo : AdeptModelLogoDark,
      aisingapore: isLight ? AisingaporeModelLogo : AisingaporeModelLogoDark,
      bigcode: isLight ? BigcodeModelLogo : BigcodeModelLogoDark,
      mediatek: isLight ? MediatekModelLogo : MediatekModelLogoDark,
      upstage: isLight ? UpstageModelLogo : UpstageModelLogoDark,
      rakutenai: isLight ? RakutenaiModelLogo : RakutenaiModelLogoDark,
      ibm: isLight ? IbmModelLogo : IbmModelLogoDark,
      'google/': isLight ? GoogleModelLogo : GoogleModelLogoDark,
      xirang: isLight ? XirangModelLogo : XirangModelLogoDark,
      hugging: isLight ? HuggingfaceModelLogo : HuggingfaceModelLogoDark,
      embedding: isLight ? EmbeddingModelLogo : EmbeddingModelLogoDark,
      perplexity: isLight ? PerplexityModelLogo : PerplexityModelLogoDark,
      sonar: isLight ? PerplexityModelLogo : PerplexityModelLogoDark,
      'bge-': BgeModelLogo,
      'voyage-': VoyageModelLogo,
    }

    // 尝试匹配模型ID
    for (const key in logoMap) {
      const regex = new RegExp(key, 'i')
      if (regex.test(modelId)) {
        return logoMap[key] || defaultIcon
      }
    }

    // 第二级匹配: 尝试直接从模型ID中提取可能的文件名
    // 移除路径和版本号等信息
    const cleanModelId = modelId
      .split('/')
      .pop() // 移除路径部分，如 THUDM/GLM-Z1-32B-0414 -> GLM-Z1-32B-0414
      ?.replace(/[\-_][0-9]+[Bb].*$/, '') // 移除版本号和后缀，如 GLM-Z1-32B-0414 -> GLM-Z1
      ?.replace(/[\-_][A-Za-z][0-9]+/, '') // 移除如Z1这样的版本标记，如 GLM-Z1 -> GLM
      ?.toLowerCase() // 转为小写

    if (cleanModelId) {
      // 针对清理后的模型ID再次尝试匹配
      for (const key in logoMap) {
        const regex = new RegExp(key, 'i')
        if (regex.test(cleanModelId)) {
          return logoMap[key] || defaultIcon
        }
      }
    }

    // 第三级匹配: 使用默认的chatgpt
    return defaultIcon
  } catch (e) {
    console.warn('加载模型图标时出错:', e)
    return defaultIcon
  }
}

/**
 * 获取模型对应的图标名称
 * 用于SvgIcon组件
 * @param modelId 模型ID
 * @returns 图标名称
 */
export function getModelIcon(modelId: string): string {
  if (!modelId) {
    return 'ri:ai-generate'
  }

  // 处理已知的特定路径模式
  if (modelId.startsWith('BAAI/bge')) {
    return 'ri:database-2-fill'
  }

  // 第一级匹配: 使用图标映射表
  const iconMap: Record<string, string> = {
    gpt: 'ri:openai-fill',
    claude: 'fa-brands:superpowers',
    qwen: 'fa-brands:centos',
    gemini: 'fe:google',
    deepseek: 'ion:infinite',
    mistral: 'fa-brands:mixcloud',
    llama: 'fa-solid:spider',
    baichuan: 'ri:robot-2-fill',
    yi: 'ri:brain-fill',
    // 添加BGE模型图标
    bge: 'ri:database-2-fill',
    // 添加GLM模型图标
    glm: 'ri:brain-line',
    chatglm: 'ri:brain-line',
    // 可以添加更多图标映射
    dianxin: 'ri:signal-tower-fill',
    moonshot: 'ri:rocket-fill',
    hunyuan: 'ri:cloud-fill',
    zhipu: 'ri:lightbulb-fill',
    grok: 'ri:brain-fill',
  }

  // 第一级匹配: 尝试使用图标映射表
  for (const key in iconMap) {
    const regex = new RegExp(key, 'i')
    if (regex.test(modelId)) {
      return iconMap[key]
    }
  }

  // 第二级匹配: 尝试直接从模型ID中提取可能的图标名称
  // 移除路径和版本号等信息
  const cleanModelId = modelId
    .split('/')
    .pop() // 移除路径部分
    ?.replace(/[\-_][0-9]+[Bb].*$/, '') // 移除版本号和后缀
    ?.replace(/[\-_][A-Za-z][0-9]+/, '') // 移除如Z1这样的版本标记
    ?.toLowerCase() // 转为小写

  // 处理特殊情况
  const specialIconMappings: Record<string, string> = {
    glm: 'ri:brain-line',
    bge: 'ri:database-2-fill',
  }

  if (cleanModelId && specialIconMappings[cleanModelId]) {
    return specialIconMappings[cleanModelId]
  }

  // 第三级匹配: 返回默认ChatGPT图标
  return 'ri:openai-fill'
}
