<script setup lang="ts">
import { computed, h, ref } from 'vue'
import { NButton, NDropdown, NInput } from 'naive-ui'
import { ElButton, ElDialog, ElImage, ElInput } from 'element-plus'
import dayjs from 'dayjs'
import AvatarComponent from './Avatar.vue'
import TextComponent from './Text.vue'
import ms from '@/utils/message'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { copyToClip } from '@/utils/copy'
import { t } from '@/locales'

interface Props {
  latestChatId?: string
  dateTime?: string
  text?: string
  inversion?: boolean
  error?: boolean
  loading?: boolean
  canRetry?: boolean
  name?: string
  previousChatId?: string
  currentChatId?: string
  index?: number
  toolExecutionList?: Chat.ToolExecution[]
  chatContentRecordList?: Chat.ChatContentRecord[]
  reasoningContent?: string
  imageInfoList?: Array<{ imageUrl: string; ossId: string; fileName?: string }>
  onImageLoad?: () => void
  readonly?: boolean
}

interface Emit {
  (ev: 'retry'): void
  (ev: 'delete'): void
  (ev: 'edit', text: string): void
}

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

// 调试图片信息
console.log('Message component props:', {
  inversion: props.inversion,
  text: props.text ? `${props.text.substring(0, 50)}...` : '',
  imageInfoList: props.imageInfoList,
  hasImages: props.imageInfoList && props.imageInfoList.length > 0,
})

// 调试 toolExecutionList
// console.log('Message component received toolExecutionList:', props.toolExecutionList)

const { isMobile } = useBasicLayout()

const textRef = ref<HTMLElement>()

const asRawText = ref(props.inversion)

const messageRef = ref<HTMLElement>()

const isHovering = ref(false)

// ElementPlus 的 ElImage 组件自带预览功能，不需要手动实现

const editingText = ref(props.loading ? '' : props.text || '')

// 新增对话框显示状态变量
const showEditDialog = ref(false)

const options = computed(() => {
  const common = []

  common.push({
    label: t('chat.copy'),
    key: 'copyText',
    icon: () => h('i', { class: 'iconfont icon-fuzhi' }),
  })
  if (!props.readonly) {
    common.push({
      label: t('common.delete'),
      key: 'delete',
      icon: () => h('i', { class: 'iconfont icon-shanchu', style: 'color: #F25B37' }),
    })
  }

  if (!props.inversion) {
    common.unshift({
      label: asRawText.value ? t('chat.preview') : t('chat.showRawText'),
      key: 'toggleRenderType',
      icon: () =>
        h('i', { class: asRawText.value ? 'iconfont icon-yulan' : 'iconfont icon-xianshi' }),
    })
  }

  return common
})

function renderLabel(option: any) {
  if (option.key === 'delete') return h('span', { style: 'color: #F25B37' }, option.label)
  else return h('span', { style: '' }, option.label)
}

function handleSelect(key: 'copyText' | 'delete' | 'toggleRenderType') {
  switch (key) {
    case 'copyText':
      handleCopy()
      return
    case 'toggleRenderType':
      asRawText.value = !asRawText.value
      return
    case 'delete':
      emit('delete')
  }
}

function handleRetry() {
  // ms.warning('该功能待接口完善')
  // messageRef.value?.scrollIntoView({ behavior: 'smooth', block: 'center' })
  emit('retry')
}

async function handleCopy() {
  try {
    // todo 复制时候，应该只拷贝展示的正文内容吧，目前think标签在正文里返回应该不要拷贝进去
    await copyToClip(props.text || '')
    ms.success(t('chat.copied'))
  } catch {
    ms.error(t('chat.copyFailed'))
  }
}

function handleConfirmEdit() {
  emit('edit', editingText.value)
  showEditDialog.value = false
}

// todo 编辑改为el-dialog弹出输入框el-input让用户编辑，而不是在这里直接编辑
// resolved 编辑改为el-dialog弹出输入框el-input让用户编辑，而不是在这里直接编辑
function handleEdit() {
  if (!props.loading) {
    editingText.value = props.text || ''
    showEditDialog.value = true
  }
}

// 计算是否需要显示分割线
const isNewContext = computed(() => {
  return (
    // 存在情况：ai回复信息被打断，导致currentChatId为空，清除上下文的previousChatId也就为空。所以这里补上一个或条件"props.index > 0"
    // resolved "props.index"可能为"未定义"。
    (props.previousChatId || (props.index ?? 0) > 0) &&
    props.previousChatId !== props.currentChatId &&
    !props.loading &&
    props.inversion
  )
})

const isFirstContent = computed(() => {
  // resolved 直接从父组件拿index来判断是不是第一条吧
  return (props.index ?? -1) === 0 && props.inversion
  // return !props.previousChatId && !props.currentChatId && props.inversion
})

// NDropdown事件处理
const handleDropdownEnter = () => {
  isHovering.value = true
}

const handleDropdownLeave = () => {
  isHovering.value = false
}

// 处理图片加载完成事件
const handleImageLoad = () => {
  // 如果父组件传递了 onImageLoad 回调，则调用它
  if (props.onImageLoad) {
    props.onImageLoad()
  }
}
</script>

<template>
  <div>
    <template v-if="isNewContext">
      <div class="flex items-center justify-center my-4">
        <div class="w-[100px] border-t border-gray-200 dark:border-gray-600" />
        <span class="mx-4 text-xs text-gray-400 dark:text-gray-500">新的上下文</span>
        <div class="w-[100px] border-t border-gray-200 dark:border-gray-600" />
      </div>
    </template>
    <template v-if="isNewContext || isFirstContent">
      <div class="flex items-center justify-center my-4">
        <span class="text-xs text-[#b4bbc4]">{{
          dayjs(props.dateTime).format('MM-DD HH:mm')
        }}</span>
      </div>
    </template>
    <!-- <div>props.latestChatId-{{ props.latestChatId }}</div> -->
    <!-- <div>props.previousChatId-{{ props.previousChatId }}</div> -->
    <!-- <div>props.currentChatId-{{ props.currentChatId }}</div> -->
    <!-- <div>!== - {{ props.previousChatId !== props.currentChatId }}</div> -->
    <!-- <div>!props.loading-{{ !props.loading }}</div> -->
    <!-- <div>props.inversion-{{ props.inversion }}</div> -->
    <div
      ref="messageRef"
      class="flex w-full mb-6 overflow-hidden"
      @mouseenter="isHovering = true"
      @mouseleave="isHovering = false"
    >
      <div
        class="flex items-center justify-center flex-shrink-0 h-8 overflow-hidden rounded-full basis-8 mr-2"
      >
        <AvatarComponent :image="props.inversion" />
      </div>
      <div class="overflow-hidden text-sm items-start w-full">
        <div class="flex items-center gap-2">
          <span class="font-bold">{{ props.inversion ? 'You' : 'Assistant' }}</span>
          <!-- <span class="text-xs text-[#b4bbc4]">{{ dateTime }}</span> -->
        </div>
        <!-- 图片显示区域 -->
        <div v-if="props.imageInfoList && props.imageInfoList.length > 0" class="mb-3">
          <div class="flex flex-wrap gap-2">
            <ElImage
              v-for="(image, imgIndex) in props.imageInfoList"
              :key="imgIndex"
              :src="image.imageUrl"
              :alt="image.fileName || `图片${imgIndex + 1}`"
              :class="
                props.imageInfoList.length === 1
                  ? 'w-[200px] h-[200px] rounded-lg border border-gray-200 dark:border-gray-600'
                  : 'w-[100px] h-[100px] rounded-lg border border-gray-200 dark:border-gray-600'
              "
              fit="cover"
              :preview-src-list="props.imageInfoList.map(img => img.imageUrl)"
              :initial-index="imgIndex"
              preview-teleported
              hide-on-click-modal
              @load="handleImageLoad"
            />
          </div>
        </div>

        <div class="flex items-end gap-1 mt-2 flex-row">
          <!-- 当AI在思考中状态时显示loading，但如果有推理内容则显示内容 -->
          <div
            v-if="
              !props.inversion &&
              props.loading &&
              (props.text === t('chat.thinking') || props.text === '') &&
              !props.reasoningContent
            "
            class="loader-wrap"
          >
            <span class="loader ml-2"></span>
          </div>
          <TextComponent
            v-else
            ref="textRef"
            :inversion="props.inversion"
            :error="props.error"
            :text="props.text"
            :loading="props.loading"
            :as-raw-text="asRawText"
            :tool-execution-list="props.toolExecutionList"
            :chat-content-record-list="props.chatContentRecordList"
            :reasoning-content="props.reasoningContent"
          />

          <!-- 移除这里的编辑框，改为在下方通过弹窗显示 -->

          <div
            v-show="!(!props.inversion && props.loading && props.text === t('chat.thinking'))"
            class="action-container flex"
            :class="{ 'action-visible': isHovering || isMobile, 'flex-col': isMobile }"
          >
            <div
              v-if="!props.inversion && props.canRetry"
              class="my-btn transition text-neutral-500 hover:text-neutral-800 dark:hover:text-neutral-500"
              @click="handleRetry"
            >
              <i class="iconfont icon-a-shuaxinzhongxinshengcheng" style="font-size: 18px"></i>
            </div>
            <div
              v-if="!readonly"
              class="my-btn transition text-neutral-500 hover:text-neutral-800 dark:hover:text-neutral-500"
              @click="handleEdit"
            >
              <i class="iconfont icon-bianji" style="font-size: 18px"></i>
            </div>
            <!-- resolved edit编辑逻辑移到此处，跟handleRetry平级  -->
            <NDropdown
              :trigger="isMobile ? 'click' : 'hover'"
              :placement="!props.inversion ? 'right' : 'left'"
              :options="options"
              :render-label="renderLabel"
              @select="handleSelect"
              @mouseenter="handleDropdownEnter"
              @mouseleave="handleDropdownLeave"
            >
              <div
                class="my-btn transition text-neutral-500 hover:text-neutral-800 dark:hover:text-neutral-500"
              >
                <i class="iconfont icon-gengduo" style="font-size: 18px"></i>
              </div>
            </NDropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <!-- todo ElDialog右上角的close有点小 -->
    <ElDialog
      v-model="showEditDialog"
      :width="isMobile ? '90%' : '1000px'"
      draggable
      append-to-body
      style="padding-top: 0"
      class="custom-dialog"
    >
      <div class="g-family text-lg font-medium mb-4 text-[#30343A]">编辑</div>
      <ElInput
        v-model="editingText"
        type="textarea"
        :autosize="{ minRows: 8, maxRows: 20 }"
        placeholder="请编辑消息内容"
      />
      <template #footer>
        <span class="dialog-footer">
          <ElButton @click="showEditDialog = false">取消</ElButton>
          <ElButton type="primary" @click="handleConfirmEdit">确定</ElButton>
        </span>
      </template>
    </ElDialog>

    <!-- 使用 ElImage 组件自带的预览功能，无需手动实现预览对话框 -->
  </div>
</template>

<style scoped lang="less">
.edit-box {
  min-width: 200px;
  max-width: 80%;
}
.my-btn {
  margin-left: 8px;
  height: 24px;
  width: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 3px;
  &:hover {
    background: rgba(76, 92, 236, 0.1);
  }
}
.action-container {
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.2s ease;
}
.action-visible {
  visibility: visible;
  opacity: 1;
}
</style>
