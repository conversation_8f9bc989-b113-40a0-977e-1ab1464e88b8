#!/bin/bash

# 阿里云云效部署脚本
# 解决 electron 安装问题

echo "开始云效部署构建..."

# 设置 npm 镜像源
npm config set registry https://registry.npmmirror.com/
npm config set electron_mirror https://npmmirror.com/mirrors/electron/
npm config set electron_builder_binaries_mirror https://npmmirror.com/mirrors/electron-builder-binaries/

# 跳过 electron 相关依赖安装
export ELECTRON_SKIP_BINARY_DOWNLOAD=1
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

echo "安装生产依赖..."
# 只安装生产依赖，跳过 devDependencies 中的 electron
npm install --production --ignore-scripts

echo "安装构建必需的开发依赖..."
# 单独安装构建必需的依赖，排除 electron 相关
npm install --no-save \
  vite \
  @vitejs/plugin-vue \
  typescript \
  vue-tsc \
  autoprefixer \
  postcss \
  tailwindcss \
  unplugin-auto-import \
  unplugin-vue-components \
  less

echo "执行构建..."
npm run build:ci

echo "构建完成！"
