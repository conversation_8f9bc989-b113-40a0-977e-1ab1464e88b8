<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { queryConversationRecord } from '@/api/chat-record'
import { ElButton, Sort } from 'element-plus'
import type { ChatRecordListResultModel, ChatRecordListParams } from '@/api/chat-record/type'
import { formatToken } from '@/utils/formatter'

const router = useRouter()

const loading = ref(false)
const recordList = ref<ChatRecordListResultModel[]>([])
const queryParams = ref<ChatRecordListParams>({
  keyword: '',
  pageNum: 1,
  pageSize: 10,
  orderByColumn: '',
  isAsc: 'asc',
})
const total = ref(0)

// 获取对话记录
const fetchRecords = async () => {
  try {
    loading.value = true
    const res = await queryConversationRecord({
      keyword: queryParams.value.keyword || '',
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
      orderByColumn: queryParams.value.orderByColumn,
      isAsc: queryParams.value.isAsc,
    })
    if (res.code === 200) {
      recordList.value = res.rows
      total.value = res.total || 0
    }
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  fetchRecords()
}

/**
 * 排序变化
 */
function sortChange({ prop, order }: Sort) {
  let isAsc = ''
  if (order === 'ascending') {
    isAsc = 'asc'
  } else if (order === 'descending') {
    isAsc = 'desc'
  }
  queryParams.value.pageNum = 1
  queryParams.value.orderByColumn = prop
  queryParams.value.isAsc = isAsc
  fetchRecords()
}

onMounted(() => {
  fetchRecords()
})

/**
 * 查看
 */
function handleLookUp(conversationId: string) {
  router.push({
    name: 'chat-detail',
    params: {
      conversationId,
    },
  })
}

function handleRowClick(row: any, column: any) {
  if (['name', 'subtitle'].includes(column.property)) {
    handleLookUp(row.id)
  }
}
</script>

<template>
  <div class="my-container">
    <div class="flex justify-end">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" class="text-right">
        <el-form-item label="" prop="keyword" class="!mr-0">
          <el-input
            v-model="queryParams.keyword"
            style="width: 400px"
            placeholder="请输入你需要搜索的内容"
            clearable
            @clear="handleQuery"
            @keyup.enter="handleQuery"
          >
            <template #suffix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <el-table
      v-loading="loading"
      :data="recordList"
      style="width: 100%"
      @sort-change="sortChange"
      @row-click="handleRowClick"
    >
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="nickName" label="用户" width="120">
        <template #default="{ row }">
          <img
            v-if="row.avatar"
            class="size-8 inline-block rounded-sm mr-2"
            :src="row.avatar"
            alt=""
          />
          <span class="align-middle">
            {{ row.nickName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="对话标题"
        show-overflow-tooltip
        class-name="cursor-pointer"
      />
      <el-table-column
        prop="subtitle"
        label="对话总结"
        show-overflow-tooltip
        class-name="cursor-pointer"
      />
      <el-table-column prop="role" label="使用助手" show-overflow-tooltip>
        <template #default="{ row }">
          <div
            class="emoji-wrap !inline-flex !w-[32px] !h-[32px] mr-2"
            :style="{ backgroundColor: row.agentBackgroundColor }"
          >
            <img v-if="row.agentEmoji" :src="row.agentEmoji" class="size-5" />
          </div>
          <span class="align-super">
            {{ row.agentName }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="tokens" label="累计模型消耗" width="150">
        <template #default="{ row }">
          <span :style="{ fontSize: row.maxTokens === -1 ? '22px' : '' }">{{
            row.maxTokens === -1 ? '∞' : `${formatToken(row.tokens)}`
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" sortable />
      <el-table-column prop="updateTime" label="最后更新时间" width="180" sortable />
      <el-table-column label="操作" width="70" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleLookUp(row.id)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      @pagination="fetchRecords"
    />
  </div>
</template>

<style scoped>
.my-container {
  padding: 20px;
}
.page-title {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}
</style>
