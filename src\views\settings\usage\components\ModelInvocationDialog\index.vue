<script lang="ts" setup>
import { formatToken } from '@/utils/formatter'
import type { AggregateDataPoint } from '@/api/usage/types'
import { getProviderLogo } from '@/utils/platforms'
import { getModelLogo } from '@/utils/models'
import peopleIcon from '@/assets/settings/usage/people-icon.svg'
import appIcon from '@/assets/settings/usage/app-icon.svg'
import { publicationTypeList } from '@/utils/external-release'
import { useAgentList } from '@/hooks/useAgentList'
import MemberTabContent from './MemberTabContent.vue'
import ApplicationTabContent from './ApplicationTabContent.vue'
import { useDeptList } from '../../hooks/useDeptList'
/**
 * 模型调用对话框标签页枚举
 */
enum ModelInvocationTab {
  /** 成员调用 */
  MEMBER = 1,
  /** 应用调用 */
  APPLICATION,
}

defineOptions({
  name: 'ModelInvocationDialog',
})

// 控制对话框显示/隐藏
const dialogVisible = defineModel({ default: false })

// 控制选中的 tab
const activeTab = ref(ModelInvocationTab.MEMBER)

const { deptOptions, getDeptList } = useDeptList()

// 标签页数据
const tabs = ref([
  { id: ModelInvocationTab.MEMBER, label: '成员调用', icon: peopleIcon },
  { id: ModelInvocationTab.APPLICATION, label: '应用调用', icon: appIcon },
])

// 应用类型
const outwardTypeOption = computed(() => {
  return publicationTypeList
    .filter(item => item.publish)
    .map(type => ({
      value: type.key,
      label: type.name,
    }))
})

const { agentList, getAgentList } = useAgentList()
getAgentList()

// 编辑数据相关
const info = ref<Partial<AggregateDataPoint>>({})
const modelId = computed(() => info.value.entityId)
const modelName = computed(() => info.value.entityName)
const platformName = computed(() => info.value.groupName)
const platformImg = computed(() => {
  return info.value?.platformIconUrl || getProviderLogo(platformName.value || '')
})
const inputTokens = computed(() => info.value.inputTokens || 0)
const outputTokens = computed(() => info.value.outputTokens || 0)
const totalTokens = computed(() => info.value.totalTokens || 0)
const timeRange = ref({ startTime: '', endTime: '' })

// 编辑方法，接收模型名称、平台名称、时间、输入tokens、输出tokens、调用总量tokens
function editData(dataPoint: AggregateDataPoint, startTime: string, endTime: string) {
  info.value = dataPoint
  timeRange.value = { startTime, endTime }
  openDialog()
}

// 打开对话框的方法
function openDialog() {
  dialogVisible.value = true
  nextTick(() => {
    getTableData()
  })
}

// 关闭对话框的方法
function closeDialog() {
  dialogVisible.value = false
  // 重置数据到初始状态
  activeTab.value = ModelInvocationTab.MEMBER
  info.value = {}
  timeRange.value = { startTime: '', endTime: '' }

  // 重置子组件状态
  resetMemberTab()
  resetApplicationTab()
}

// 设置选中的 tab
function setActiveTab(tab: ModelInvocationTab) {
  activeTab.value = tab
}

// 成员tab组件引用
const memberTabRef = ref<InstanceType<typeof MemberTabContent> | null>(null)

// 应用tab组件引用
const applicationTabRef = ref<InstanceType<typeof ApplicationTabContent> | null>(null)

// 重置成员tab
function resetMemberTab() {
  if (memberTabRef.value) {
    memberTabRef.value.resetAll()
  }
}

// 重置应用tab
function resetApplicationTab() {
  if (applicationTabRef.value) {
    applicationTabRef.value.resetAll()
  }
}

// 获取tab 对应的表格的数据
function getTableData() {
  if (activeTab.value === ModelInvocationTab.MEMBER && memberTabRef.value) {
    memberTabRef.value.fetchTableData()
  } else if (activeTab.value === ModelInvocationTab.APPLICATION && applicationTabRef.value) {
    applicationTabRef.value.fetchTableData()
  }
}

// 监听tab切换
watch(activeTab, () => {
  if (!dialogVisible.value) {
    return
  }
  // 重置搜索相关的变量
  if (activeTab.value === ModelInvocationTab.MEMBER) {
    // 切换到成员调用tab，重置应用调用相关的变量
    resetApplicationTab()
  } else {
    // 切换到应用调用tab，重置成员调用相关的变量
    resetMemberTab()
  }

  // 延迟获取数据，确保重置已完成
  nextTick(getTableData)
})

onMounted(() => {
  getDeptList()
})

defineExpose({
  editData,
})
</script>

<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      width="950"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog"
    >
      <template #header>
        <span class="title g-family-medium">模型调用量明细</span>
      </template>
      <div class="model-invocation">
        <div class="model-invocation-header">
          <div class="model-invocation-name">
            <img
              v-if="platformImg"
              class="size-10 mr-3 rounded-lg"
              :src="platformImg"
              alt="模型logo"
            />
            <span class="g-family-medium">{{ platformName }}</span>
            <span class="mx-6">/</span>
            <img
              v-if="modelName"
              class="size-10 mr-3 rounded-lg"
              :src="getModelLogo(modelName, '')"
              alt="模型logo"
            />
            <span class="g-family-medium">{{ modelName }}</span>
          </div>
          <div class="model-invocation-time">
            {{ timeRange.startTime }} 至 {{ timeRange.endTime }}
          </div>
        </div>
        <div class="model-invocation-content">
          <div>
            <div class="model-invocation-title">输入Tokens</div>
            <div class="model-invocation-num">{{ formatToken(inputTokens) }}</div>
          </div>
          <div>
            <div class="model-invocation-title">输出Tokens</div>
            <div class="model-invocation-num">{{ formatToken(outputTokens) }}</div>
          </div>

          <div>
            <div class="model-invocation-title">调用总量Tokens</div>
            <div class="model-invocation-num">{{ formatToken(totalTokens) }}</div>
          </div>
        </div>
      </div>

      <div class="model-invocation-tabs mb-6">
        <div class="model-invocation-tabs__nav">
          <template v-for="tab in tabs" :key="tab.id">
            <input
              type="radio"
              :id="`radio-${tab.id}`"
              class="model-invocation-tabs__radio"
              :value="tab.id"
              v-model="activeTab"
            />
            <label
              class="model-invocation-tabs__tab"
              :class="{ 'model-invocation-tabs__tab--active': activeTab === tab.id }"
              :for="`radio-${tab.id}`"
              @click="setActiveTab(tab.id)"
            >
              <img
                class="mr-2 model-invocation-tabs__icon"
                :class="{ 'model-invocation-tabs__icon--active': activeTab === tab.id }"
                :src="tab.icon"
                :alt="tab.label"
              />
              <span>{{ tab.label }}</span>
            </label>
          </template>
          <span
            class="model-invocation-tabs__glider"
            :class="`model-invocation-tabs__glider--${activeTab}`"
          ></span>
        </div>
      </div>

      <!-- Tab内容区域 -->
      <div class="tab-content" v-if="dialogVisible">
        <transition-group name="tab-fade">
          <MemberTabContent
            class="tab-content__item"
            v-show="activeTab === ModelInvocationTab.MEMBER"
            ref="memberTabRef"
            key="member"
            :model-id="modelId!"
            :time-range="timeRange"
            :dept-options="deptOptions"
          />
          <ApplicationTabContent
            class="tab-content__item"
            v-show="activeTab === ModelInvocationTab.APPLICATION"
            ref="applicationTabRef"
            key="application"
            :model-id="modelId!"
            :time-range="timeRange"
            :outward-type-option="outwardTypeOption"
            :agent-list="agentList"
          />
        </transition-group>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.title {
  font-size: 16px;
  margin-bottom: 12px;
}
.model-invocation {
  background: #f7f8fa;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 28px;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-name {
    flex: 1;
    display: inline-flex;
    align-items: center;
    font-size: 18px;
    margin-bottom: 20px;
  }

  &-time {
    color: #646a73;
    font-size: 14px;
  }

  &-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }

  &-title {
    font-size: 16px;
    margin-bottom: 8px;
  }

  &-num {
    font-size: 28px;
    font-weight: 600;
    color: #4c5cec;
  }
}

.search-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input,
.search-select {
  width: 200px;
}

.search-button {
  height: 40px;
}

.model-invocation-tabs {
  display: inline-flex;
  position: relative;
  background: rgba(218, 221, 232, 0.5);
  padding: 4px;
  border-radius: 8px;

  &__nav {
    display: inline-flex;
    position: relative;
  }

  &__radio {
    display: none;
  }

  &__tab {
    width: 115px;
    height: 40px;
    line-height: calc(40px - 24px);
    padding: 10px 12px;
    font-size: 16px;
    cursor: pointer;
    transition: color 0.15s ease-in;
    z-index: 2;
    white-space: nowrap;

    &--active {
      color: #4c5cec;
    }
  }

  &__icon {
    filter: brightness(0) saturate(100%) invert(14%) sepia(8%) saturate(1238%) hue-rotate(172deg)
      brightness(94%) contrast(92%); // #30343A
    transition: filter 0.15s ease-in;
    display: inline-block;
    vertical-align: sub;

    &--active {
      filter: brightness(0) saturate(100%) invert(42%) sepia(42%) saturate(1543%) hue-rotate(202deg)
        brightness(99%) contrast(92%); // #4C5CEC
    }
  }

  &__glider {
    position: absolute;
    display: flex;
    width: 115px;
    height: 40px;
    background-color: #fff;
    z-index: 1;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    transition: 0.25s ease-out;

    &--1 {
      transform: translateX(0);
    }

    &--2 {
      transform: translateX(100%);
    }

    &--3 {
      transform: translateX(200%);
    }
  }
}
.tab-content {
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  &__item {
    width: 100%;
    display: inline-block;
    vertical-align: top;
  }
}
/* 标签页切换动画 */
.tab-fade-enter-active,
.tab-fade-leave-active {
  transition: all 0.3s ease-out;
}

.tab-fade-enter-from {
  transform: translateX(100px);
  opacity: 0;
}

.tab-fade-leave-to {
  transform: translateX(-100px);
  opacity: 0;
}
</style>
